import styled from 'styled-components';

// API窗口样式
export const TestApiWindowView = styled.div`
  position: fixed;
  bottom: 50px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  padding: 12px;
  max-width: 300px;
  height: 40vh;
  overflow-y: auto;
  z-index: 9999;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease;

  .window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    h2 {
      margin: 0;
      font-size: 16px;
      color: #ffcc00;
    }

    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0 5px;
      line-height: 1;

      &:hover {
        color: #ff4444;
      }
    }
  }

  .api-category {
    margin-bottom: 16px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #ffcc00;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding-bottom: 4px;
    }

    .api-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
  }

  .api-button {
    background-color: #2a2a2a;
    color: white;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background-color: #444;
      border-color: #666;
    }

    &:active:not(:disabled) {
      transform: scale(0.98);
      background-color: #555;
    }

    &.loading {
      background-color: #3a3a3a;
      color: #aaa;
      cursor: wait;
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        height: 2px;
        width: 100%;
        background: linear-gradient(90deg, #ffcc00, #ff4444);
        animation: loading 1.5s infinite linear;
      }
    }

    &:disabled {
      background-color: #222;
      color: #666;
      border-color: #333;
      cursor: not-allowed;
    }
  }

  @keyframes loading {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* 自定义滚动条 */

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

// 切换按钮样式
export const ToggleButton = styled.button<{ isVisible: boolean }>`
  position: fixed;
  bottom: 10px;
  right: 10px;
  background-color: ${(props) => (props.isVisible ? '#ff4444' : '#2a2a2a')};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  z-index: 9999;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);

  &:hover {
    background-color: ${(props) => (props.isVisible ? '#ff6666' : '#444')};
  }

  &:active {
    transform: scale(0.98);
  }
`;
