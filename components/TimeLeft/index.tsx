import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react';
import { AnimatePresence, useAnimate } from 'motion/react';
import { AppGameApiKey, useMyPlayer } from '@/game/TSX/Character/MyPlayer';
import {
  <PERSON><PERSON>,
  Styled<PERSON><PERSON><PERSON><PERSON>,
  StyledHurryUp,
  StyledParagraph,
  StyledTimeLeft,
  TimeCard,
  TimeContainer,
} from './style';
import { useAppSelector } from '@/hooks/useStore';
import { TreeConfig } from '@/game/Config/TreeConfig';

const HurryUp = () => {
  return (
    <StyledHurryUp
      initial={{ opacity: 0, scale: 0, rotate: 0 }}
      animate={{
        opacity: [1, 1],
        scale: [0.9, 1, 1.1, 1, 0.9],
        rotate: [9.265, 8.265, 11.265, 8.265, 9.265],
        transition: {
          duration: 0.5,
          repeat: Infinity,
          repeatType: 'loop',
        },
      }}
      // exit={{ opacity: 0, scale: 0, rotate: 0 }}
    >
      <div>
        <span>Hurry Up</span>
      </div>
    </StyledHurryUp>
  );
};

interface TimeProps {
  time: number; // 倒计时总秒数
  onFinish?: () => void; // 倒计时结束回调
  isTimeLeft?: boolean; // 进入冷却时间
}
export const Time = forwardRef(({ time, onFinish, isTimeLeft }: TimeProps, ref: any) => {
  const [countdown, setCountdown] = useState(time);

  useEffect(() => {
    // 初始化倒计时
    setCountdown(time);
  }, [time]);

  // 格式化时间
  const formatTime = () => {
    const minutes = Math.floor(countdown / 60);
    const seconds = countdown % 60;

    // 分钟的两位数
    const minutesTens = Math.floor(minutes / 10);
    const minutesOnes = minutes % 10;

    // 秒钟的两位数
    const secondsTens = Math.floor(seconds / 10);
    const secondsOnes = seconds % 10;

    return {
      minutesTens,
      minutesOnes,
      secondsTens,
      secondsOnes,
    };
  };

  const { minutesTens, minutesOnes, secondsTens, secondsOnes } = formatTime();

  return (
    <TimeContainer ref={ref}>
      <TimeCard $isTimeLeft={isTimeLeft}>{minutesTens}</TimeCard>
      <TimeCard $isTimeLeft={isTimeLeft}>{minutesOnes}</TimeCard>

      <Colon>
        <div className="dot"></div>
        <div className="dot"></div>
      </Colon>

      <TimeCard $isTimeLeft={isTimeLeft}>{secondsTens}</TimeCard>
      <TimeCard $isTimeLeft={isTimeLeft}>{secondsOnes}</TimeCard>
    </TimeContainer>
  );
});

Time.displayName = 'Time';

const TimeLeft = () => {
  const timeLeftSecond = useTimeLeft();
  const isTimeLeft = useMemo(() => {
    return timeLeftSecond <= 30;
  }, [timeLeftSecond]);
  const myPlayer = useMyPlayer();

  const handleClick = () => {
    myPlayer.callAppApi(AppGameApiKey.activityRule, 4);
  };
  const [confettiRef, animate] = useAnimate();
  const [timeRef, timeAnimate] = useAnimate();

  useEffect(() => {
    if (isTimeLeft && timeLeftSecond >= 0) {
      animate(confettiRef.current, {
        rotate: [0, -2, -7, -2, -7, -2, 0],
        transition: {
          duration: 0.5,
          repeat: Infinity,
          repeatType: 'loop',
        },
      });

      timeAnimate(timeRef.current, {
        rotate: [0, -5, 5, -5, 5, 0],
        x: [0, '-0.3125rem', '0.3125rem', '-0.3125rem', '0.3125rem', 0],
        scale: [1, 1.1, 0.9, 1],
        transition: {
          duration: 0.2,
          repeat: Infinity,
          repeatType: 'loop',
          ease: 'linear',
        },
        delay: 0.2,
      });
    }
  }, [animate, isTimeLeft, timeAnimate, timeLeftSecond]);

  return (
    <AnimatePresence initial={false}>
      {timeLeftSecond >= 0 && (
        <StyledTimeLeft
          $bgSrc="/image/easterEgg/timeLeft_orderTree_bg.webp"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          onClick={handleClick}>
          <StyledParagraph data-text={'Time Left'}>Time Left</StyledParagraph>
          <Time ref={timeRef} time={timeLeftSecond} isTimeLeft={isTimeLeft} />

          {isTimeLeft && <HurryUp />}
          <StyledConfetti
            ref={confettiRef}
            src={'/image/easterEgg/timeLeft_confetti.webp'}
            alt="confetti"
            width={356}
            height={201}
          />
        </StyledTimeLeft>
      )}
    </AnimatePresence>
  );
};

export default TimeLeft;

enum TIME_LEFT_STATUS {
  init,
  start,
  success,
  failed,
  expired,
}

export function useTimeLeft() {
  const myPlayer = useMyPlayer();
  const [timeLeftSecond, setTimeLeftSecond] = useState(-1);
  const orderTreeEndTimeRef = useRef<number>(-1);
  const { btcAddress } = useAppSelector((state) => state.AppReducer);

  const [successFlag, setSuccessFlag] = useState<TIME_LEFT_STATUS>(0);
  const latestFlagRef = useRef(successFlag);
  const latestTimeLeftRef = useRef(timeLeftSecond);
  latestFlagRef.current = successFlag;
  latestTimeLeftRef.current = timeLeftSecond;
  const timerRef = useRef<NodeJS.Timer>();
  const rest = () => {
    setTimeLeftSecond(-1);
    orderTreeEndTimeRef.current = -1;
    setSuccessFlag(0);
  };
  useEffect(() => {
    rest();
  }, [btcAddress]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.setOrderTreeTimeLeft, (endTime: number, successFlag = 0) => {
        const now = Date.now();
        setSuccessFlag(successFlag);
        if (now > endTime) {
          orderTreeEndTimeRef.current = -1;
          setTimeLeftSecond(-1);
        } else {
          orderTreeEndTimeRef.current = endTime;
          setTimeLeftSecond(Math.floor((orderTreeEndTimeRef.current - now) / 1000));
        }
      });
    }
  }, [myPlayer]);

  useEffect(() => {
    timerRef.current = setInterval(() => {
      if (latestTimeLeftRef.current < 0) {
        clearInterval(timerRef.current);
        return;
      }
      const now = Date.now();
      const second = Math.floor((orderTreeEndTimeRef.current - now) / 1000);
      if (second < 0 && latestFlagRef.current === 1) {
        clearInterval(timerRef.current);
        setSuccessFlag(4);
      }
      setTimeLeftSecond(second);
    }, 500);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        if (latestFlagRef?.current === 4) {
          myPlayer.callAppApi(AppGameApiKey.showEasterEggFailed);
          rest();
          TreeConfig.getInstance().clearEasterEggTree();
        }
      }
    };
  }, [btcAddress, orderTreeEndTimeRef.current]);

  useEffect(() => {
    if (timeLeftSecond < 0 && timerRef.current) {
      clearInterval(timerRef.current);
    }
  }, [timeLeftSecond]);

  useEffect(() => {
    if (successFlag === 4) {
      myPlayer.callAppApi(AppGameApiKey.showEasterEggFailed);
      rest();
      TreeConfig.getInstance().clearEasterEggTree();
    }
  }, [successFlag]);

  return timeLeftSecond;
}
