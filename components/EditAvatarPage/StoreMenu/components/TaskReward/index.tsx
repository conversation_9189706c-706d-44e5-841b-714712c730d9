import Dialog from '@/commons/Dialog';
import { forwardRef, useImperativeHandle, useState } from 'react';
import styled from 'styled-components';
import rewardBg from '/public/image/reward-bg.webp';
import rewardTitle from '/public/image/claim-rewards.png';
// import rewardAxe1 from "/public/image/rewardsAxe-1.png";
// import potato from "/public/image/wangcai.png";
import Image from 'next/image';
import StartButton from '@/components/EventRules/components/StartButton';
import Star1 from '/public/image/star1.svg';
import Star2 from '/public/image/star2.svg';
import { Close } from '@/components/BasicCommunityModalContent';

export interface RewardsProps {
  onCloseCallback?: () => void;
  onClaimReward?: () => void;
}

// 定义暴露给父组件的引用接口
export interface RewardsRef {
  open: (rewardInfo: RewardInfo) => void;
  close: () => void;
  setLoading: (loading: boolean) => void;
}

interface RewardInfo {
  title: string;
  rewardList: any[];
  count: number;
}

const Container = styled.div`
  width: 46.5rem;
  height: 27.5rem;
  /* background-image: url(${rewardBg.src});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; */
  border-radius: 4rem;
  background: #f6e9dd;
  box-shadow:
    0 0 0.25rem 0 #ffb43d,
    0 0 0 0.75rem #ff8316 inset;

  position: relative;
  // 定义一个伪类affter， 这个元素用于弹窗背景，需要绘制一些淡黄色的朦胧感，主要是围绕着整个父元素，层级要最低
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 169, 69, 0.9); /* 淡黄色半透明背景 */
    filter: blur(1.25rem); /* 添加模糊效果增加朦胧感 */
    z-index: -1; /* 确保在底层 */
    border-radius: 2.5rem;
    pointer-events: none; /* 确保不影响交互 */
    transform: translateZ(0);
  }
  .reward-title {
    width: 22.5rem;
    height: 4rem;
    background-image: url(${rewardTitle.src});
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background-position: bottom;
  }

  .rewards-content {
    width: 100%;
    height: calc(100% - 6.25rem);
    display: flex;
    align-items: center;
    justify-content: start;
    padding-top: 10%;
    flex-direction: column;
    gap: 0.375rem;
    & > p {
      color: #140f08;
      margin: 0;
      padding: 0;
    }
    .rewards-content-item {
      font-size: 1.25rem;
      word-wrap: break-word;
      width: 100%;
      text-align: center;
      color: #140f08;
      font-weight: 900;
      text-shadow: 0 0 0.03125rem #000;
      -webkit-text-stroke: 0.03125rem #000;
    }
    .following {
      text-shadow: none;
      width: 100%;
      -webkit-text-stroke: 0rem;
    }
  }
  .star1 {
    position: absolute;
    top: 10%;
    left: -4%;
  }
  .star2 {
    position: absolute;
    top: 25%;
    left: -6%;
  }
  .group-jinbi {
    position: absolute;
    bottom: -5%;
    right: -8%;
    background: url('/image/start-group.webp');
    background-size: 10.375rem 12rem;
    background-position: center;
    background-repeat: no-repeat;
    width: 10.375rem;
    height: 12rem;
  }
`;
const RewardNumber = styled.div`
  border-radius: 1.875rem;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.25rem 0rem;
  box-sizing: border-box;
  width: 80%;
  height: 9.375rem;
  /* 更强烈边框内凹立体效果 */
  box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15);

  /* 使用渐变背景增强立体感 */
  background-color: #f7e7cd;

  /* 去除所有边框 */
  border: none;
  outline: none;
  gap: 0.625rem;
`;

const StyledCloseIcon = styled(Close)`
  top: 0;
  transform: translate(-2.25rem, -20%);
`;

const RequirementImage = styled.div`
  width: 6.25rem;
  height: 6.25rem;
  border-radius: 1rem;
  background-color: #faf3e5;
  border: 0.0625rem solid #c2b8a2;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  box-shadow: 0 0 0.3125rem rgba(0, 0, 0, 0.3);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  .material-icon {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 6;
  }
  .reward-count {
    position: absolute;
    bottom: 0;
    left: 0.4375rem;
    color: #fff;
    border-radius: 0.75rem;
    padding: 0 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    text-shadow:
      -0.0625rem 0.0625rem 0 #a58061,
      0.0625rem 0.0625rem 0 #a58061,
      0.0625rem -0.0625rem 0 #a58061,
      -0.0625rem -0.0625rem 0 #a58061;
  }
`;

const TaskReward = forwardRef<RewardsRef, RewardsProps>((props, ref) => {
  const { onCloseCallback, onClaimReward } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rewardInfo, setRewardInfo] = useState<RewardInfo>({
    title: '',
    rewardList: [],
    count: 0,
  });

  useImperativeHandle(ref, () => ({
    open: (info: RewardInfo) => {
      setRewardInfo(info);
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
    setLoading: (loading: boolean) => setIsLoading(loading),
  }));

  const onClaim = () => {
    if (onClaimReward) {
      onClaimReward();
    } else {
      setIsOpen(false);
      onCloseCallback?.();
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    onCloseCallback?.();
  };

  return (
    <Dialog isOpen={isOpen} onClose={handleClose}>
      <Container>
        <div className="reward-title"></div>
        <StyledCloseIcon onClick={handleClose} />
        <Image
          src={Star1}
          alt="star1"
          className="star1"
          width={60}
          height={60}
          style={{
            width: '3.75rem',
            height: '3.75rem',
          }}
        />
        <Image
          src={Star2}
          alt="star2"
          className="star2"
          width={34}
          height={34}
          style={{
            width: '2.125rem',
            height: '2.125rem',
          }}
        />
        <div className="rewards-content">
          <p className="rewards-content-item">Congratulations!</p>
          <p className="rewards-content-item">You have completed {rewardInfo.title},</p>

          <p className="rewards-content-item following">Please claim your reward</p>
          <RewardNumber>
            {rewardInfo.rewardList.map((item, index) => (
              <RequirementImage key={index}>
                <Image
                  src={item.url}
                  alt=""
                  width={80}
                  height={80}
                  style={{
                    width: '5rem',
                    height: '5rem',
                  }}
                />
                {item.quantity > 0 && <div className="reward-count">{item.quantity}</div>}
              </RequirementImage>
            ))}
          </RewardNumber>
          <StartButton text="Claim" onClick={onClaim} loading={isLoading} />
        </div>
        <div className="group-jinbi" />
      </Container>
    </Dialog>
  );
});

TaskReward.displayName = 'TaskReward';
export { TaskReward };
