import { IS_RELEASE_ONLINE } from '@/constant';
import { createTemplateContext } from '@/utils/createTemplateContext';

export enum TAB_ENUM {
  THING_BAG,
  MATERIAL_BAG,
  PET_BAG,

  // SYNTHETIC_BAG,
}

export const tabList = IS_RELEASE_ONLINE
  ? [
      { tab: TAB_ENUM.THING_BAG, svgIconName: 'pickaxe' },
      { tab: TAB_ENUM.MATERIAL_BAG, svgIconName: 'fish' },
    ]
  : [
      { tab: TAB_ENUM.THING_BAG, svgIconName: 'pickaxe' },
      { tab: TAB_ENUM.MATERIAL_BAG, svgIconName: 'fish' },
      { tab: TAB_ENUM.PET_BAG, svgIconName: 'pet' },
    ];

interface BagInventoryContextState {
  tab: TAB_ENUM;
  activeIndex: number;
  bagDragItem: string;
}

const initState: BagInventoryContextState = {
  tab: TAB_ENUM.THING_BAG,
  activeIndex: 0,
  bagDragItem: '',
};

const {
  TemplateContextProvider: BagInventoryContextProvider,
  useContextDispatch: useBagContextDispatch,
  useContextSelector: useBagContextSelector,
} = createTemplateContext<BagInventoryContextState>(initState);

BagInventoryContextProvider.displayName = 'BagInventoryContextProvider';

export { BagInventoryContextProvider, useBagContextDispatch, useBagContextSelector };
