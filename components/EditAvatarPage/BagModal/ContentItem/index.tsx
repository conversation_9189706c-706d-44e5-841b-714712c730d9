import { ContentItemView, TooltipBoxView } from './style';
import NewPng from '/public/image/bag/new.png';
import { useRef, useMemo, useState } from 'react';
import Tooltip, { TooltipRef } from 'rc-tooltip';
import useBagInventory from '../../../../hooks/useBagInventory';
import { INVENTORY_TYPE_ENUM } from '@/constant/enum';
import Image from 'next/image';
import { useAppSelector } from '@/hooks/useStore';
import styled from 'styled-components';
import { changePetNewFlag, removeIsNewState } from '@/server';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import useToolTipPortraitFix from '@/hooks/useToolTipPortraitFix';

export interface IDetails {
  name: string;
  description: string;
  quantity?: number;
  maxDurability?: number;
  currentDurability?: number;
  type: INVENTORY_TYPE_ENUM;
  expirationTime?: string;
  score?: number;
  trait?: string;
  quality?: number; // 品质：1白，2绿，3蓝，4紫，5黄，6红，7彩
}

interface IProps {
  src?: string;
  num?: number;
  check?: boolean;
  isNew?: boolean;
  itemId?: string;
  questKey?: string;
  redMark?: boolean;
  showDetail?: IDetails; //鼠标移入显示详情
  disabled?: boolean;
  onClick?: (e?: any) => void;
  boxShadow?: string;
  draggable?: boolean;
  isGlowWeapon?: boolean;
  currentDurability?: number;
  tooltipBoxRender?: (...props: any[]) => React.ReactNode;
  [key: string]: any;
  isDragging?: boolean;
  isBind?: boolean;
  showBind?: boolean;
  ItemSlots?: (...props: any[]) => React.ReactElement;
}

export default function ContentItem({
  src,
  num,
  check,
  isNew,
  questKey,
  redMark,
  disabled,
  showDetail,
  onClick,
  boxShadow,
  draggable,
  itemId,
  isGlowWeapon,
  currentDurability,
  isDragging,
  style = {},
  tooltipBoxRender,
  isBind = false,
  showBind = false,
  ItemSlots,
  ...targetArg
}: IProps) {
  const { getBagInventoryListDta } = useBagInventory();
  const isPortrait = useAppSelector((state) => state.AppReducer.isPortrait);
  const triggerRef = useRef<TooltipRef>(null);
  const [active, setActive] = useState<boolean>(false);
  const { updateGameStatePetItemData } = useUpdatePetListItem();

  const handleChangePetStatus = async (petId: string) => {
    try {
      const res = await changePetNewFlag(petId);
      if (res.data.code === 1) {
        const newPetData = res.data.data;
        updateGameStatePetItemData({ followList: [], targetPet: newPetData });
      }
    } catch (error) {}
  };

  const setIsNew = () => {
    if (isNew && itemId) {
      if (showDetail?.type === INVENTORY_TYPE_ENUM.pet) {
        handleChangePetStatus(itemId);
      } else {
        removeIsNewState(itemId)
          .then((res) => {
            if (res.data.code === 1) {
              getBagInventoryListDta();
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    }
  };

  const overlay = useMemo(() => {
    if (tooltipBoxRender) return tooltipBoxRender;
    return showDetail && !isDragging && <TooltipBox showDetail={showDetail} />;
  }, [isDragging, showDetail, tooltipBoxRender]);

  const trigger = showDetail?.type === INVENTORY_TYPE_ENUM.pet ? 'click' : 'hover';

  const { classStyle, handleVisibleChange } = useToolTipPortraitFix(triggerRef);

  return (
    <>
      <ContentItemView
        check={check || active}
        disabled={disabled}
        boxShadow={boxShadow}
        onClick={() => onClick && onClick()}
        {...targetArg}
        draggable={draggable}
        effect={showDetail?.type === INVENTORY_TYPE_ENUM.equipment && !!showDetail?.trait}
        isGlowWeapon={isGlowWeapon}
        currentDurability={currentDurability}
        style={style}>
        {currentDurability === 1 && (
          <RedEffect>
            <div></div>
          </RedEffect>
        )}
        {src && (
          <Tooltip
            zIndex={99}
            trigger={trigger}
            ref={triggerRef}
            classNames={isPortrait ? { root: classStyle } : {}}
            onVisibleChange={(visible) => {
              handleVisibleChange(visible);
              setActive(!!(visible && showDetail));
            }}
            overlay={overlay}
            showArrow={false}
            align={
              isPortrait
                ? {
                    points: ['tl', 'bl'],
                    offset: ['0%', '0%'],
                    useCssBottom: true,
                    useCssRight: true,
                    overflow: { adjustX: false, adjustY: false },
                  }
                : {
                    points: ['tl', 'cl'],
                    offset: ['20.18%', '12.5%'],
                  }
            }>
            {ItemSlots ? (
              <ItemSlotsWrapper>
                <ItemSlots
                  onDragStart={(e: any) => {
                    if (!draggable) {
                      e.preventDefault();
                    }
                  }}
                  onClick={setIsNew}
                />
              </ItemSlotsWrapper>
            ) : (
              <StyledItem
                $bgSrc={src}
                onDragStart={(e: any) => {
                  if (!draggable) {
                    e.preventDefault();
                  }
                }}
                onClick={setIsNew}
              />
            )}
          </Tooltip>
        )}
        <span className="content-num">{num}</span>
        {questKey !== undefined ? (
          <span className="quest-key">{questKey}</span>
        ) : (
          !check &&
          isNew && <Image width={48} height={48} src={NewPng.src} alt="" className="content-new" />
        )}
        {/* redMark 该物品当前是否支持合成 */}
        {/* {redMark && <span className="red-mark" />} */}
      </ContentItemView>
      {isBind && showBind && (
        <StyledSvgWrapper>
          <SpriteSvg id="bind" />
        </StyledSvgWrapper>
      )}
    </>
  );
}

const StyledItem = styled.div<{ $bgSrc: string }>`
  width: 100%;
  height: 100%;
  border-radius: 1.25rem;
  position: absolute;
  padding: 0.25rem;
  box-sizing: border-box;
  z-index: 7;
  left: 0;
  top: 0;
  background-image: url(${(props) => props.$bgSrc});
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
`;

const RedEffect = styled.div`
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  border-radius: 1.25rem;
  pointer-events: none;
  & > div {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 1.25rem;
    filter: blur(0.4375rem) opacity(1);
    animation: shine 1s linear infinite;
    pointer-events: none;
    &::before {
      pointer-events: none;
      position: absolute;
      content: '';
      display: block;
      border: 0.5rem solid red;
      border-radius: 1.25rem;

      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }
  }
  @keyframes shine {
    50% {
      filter: blur(0.4375rem) opacity(0.7);
    }
  }
`;

const ItemSlotsWrapper = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  position: absolute;
  top: 0.4375rem;
  left: 0.625rem;
  z-index: 9;
`;

export function TooltipBox({
  showDetail,
  className,
}: {
  showDetail: IDetails;
  className?: string;
}) {
  return (
    <TooltipBoxView quality={showDetail.quality} className={className}>
      <h3 className="tooltip-box-title">{showDetail.name}</h3>
      <div>
        <p>
          Name: <span>{showDetail.name}</span>
        </p>
        {showDetail.description && (
          <p>
            Description: <span>{showDetail.description}</span>
          </p>
        )}

        {showDetail.quantity !== undefined && (
          <p>
            Quantity: <span>{showDetail.quantity}</span>
          </p>
        )}
        {showDetail.maxDurability !== undefined && (
          <p>
            Max Durability: <span>{showDetail.maxDurability}</span>
          </p>
        )}
        {showDetail.currentDurability !== undefined && (
          <p>
            Current Durability:{' '}
            <span
              style={{
                color: showDetail.currentDurability === 1 ? '#FF2727' : '',
              }}>
              {showDetail.currentDurability}
            </span>
          </p>
        )}
        {showDetail.expirationTime && (
          <p>
            Expiration Time: <span>{showDetail.expirationTime}</span>
          </p>
        )}
        {showDetail.score !== undefined && (
          <p>
            Score: <span>{showDetail.score}</span>
          </p>
        )}
        {showDetail.trait !== undefined && (
          <p>
            Trait: <span>{showDetail.trait}</span>
          </p>
        )}
      </div>
    </TooltipBoxView>
  );
}
