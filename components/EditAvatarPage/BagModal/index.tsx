import { BagInventoryView, BagModalBox, BagModalView, BagQuickBarView } from './style';
import Modal from '../BasicComponents/Modal';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IBagInventoryItem, STORAGE_MENU_ENUM } from '../../../constant/type';
import ContentItem from './ContentItem';
import LocalLoading from '@/components/LoadingContent';

import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';
import useBagInventory from '../../../hooks/useBagInventory';
import { setEquipmentShortcut } from '../../../server';
import toast from 'react-hot-toast';
import { setStorageMenu } from '../../../store/app';
import BagInventoryTabs from './BagInventoryTabs';
import OutfitList from './OutfitList';
import MateriaList from './MateriaList';
import { ConfigManager } from '@/game/Config/ConfigManager';
import { getDurabilityTips } from '@/utils/durabilityTips';
import DeleteModal, { DeleteModalRef } from './DeleteModal';
import {
  BagInventoryContextProvider,
  TAB_ENUM,
  useBagContextDispatch,
  useBagContextSelector,
} from './context';
import PetList from './PetList';
import { INVENTORY_TYPE_ENUM } from '@/constant/enum';
import { motion, useAnimate } from 'framer-motion';

function BagModalInner({ showOnly }: { showOnly?: boolean }) {
  const { storageMenu } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const dispatch = useDispatch();

  const bagDragItem = useBagContextSelector((state) => state.bagDragItem);
  const contextDispatch = useBagContextDispatch();

  /**
   * bagInventoryList： 物品列表数据
   * syntheticsList： 合成列表数据
   * getBagInventoryListDta： 获取物品列表数据
   * getSyntheticsListData： 获取合成列表数据
   */
  const { bagInventoryList, fetchAllData, isPending, materialList, getBagInventoryListDta } =
    useBagInventory(false);
  // 添加一个状态来跟踪弹窗是否可见
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [deleteHighlight, setDeleteHighlight] = useState(false);
  const deleteModalRef = useRef<DeleteModalRef>(null);
  // 监听弹窗可见性变化
  useEffect(() => {
    const visible = storageMenu === STORAGE_MENU_ENUM.BAG_MENU || showOnly === true;
    setIsVisible(visible);
  }, [storageMenu, showOnly]);

  // 销毁物品逻辑
  const handleDropToDelete = async (item: any) => {
    if (item && item.userItemId) {
      deleteModalRef.current?.open(item);
      setDeleteHighlight(false);
      setIsDragging(false);
    }
  };
  const handleDragOverToDelete = () => setDeleteHighlight(true);
  const handleDragLeaveToDelete = () => setDeleteHighlight(false);
  const activeIndex = useBagContextSelector((state) => state.activeIndex);

  const handleQuickBarDragStart = (data: IBagInventoryItem & { fromQuickBar: boolean }) => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        bagDragItem: JSON.stringify(data),
      },
    });
  };
  const handleQuickBarDragEnd = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        bagDragItem: '',
      },
    });
  };

  const ref = useRef<HTMLDivElement>(null);

  return (
    <div ref={ref} id="testPortalEL">
      <Modal
        visible={isVisible}
        emptyOnly={true}
        zIndex={9}
        popupClose={true}
        onClose={() => {
          setIsVisible(false);
          dispatch(setStorageMenu(null));
        }}
        portalContainer={ref.current as Element}>
        <BagModalView>
          <BagModalBox
            $activeIndex={activeIndex}
            onClick={(e: any) => {
              e.stopPropagation();
            }}
            id="bagModalView">
            <BagInventory
              setIsDragging={setIsDragging}
              isVisible={isVisible}
              isDragging={isDragging}
              deleteHighlight={deleteHighlight}
              onDropToDelete={handleDropToDelete}
              onDragOverToDelete={handleDragOverToDelete}
              onDragLeaveToDelete={handleDragLeaveToDelete}
              bagInventoryList={bagInventoryList}
              materialList={materialList}
              isPending={isPending}
              fetchAllData={fetchAllData}
            />
            <BagQuickBar
              isDragging={isDragging}
              setIsDragging={setIsDragging}
              bagDragItem={bagDragItem}
              handleQuickBarDragStart={handleQuickBarDragStart}
              handleQuickBarDragEnd={handleQuickBarDragEnd}
            />
          </BagModalBox>
        </BagModalView>
      </Modal>
      <DeleteModal
        ref={deleteModalRef}
        onComplete={() => {
          getBagInventoryListDta();
        }}
      />
    </div>
  );
}

export default function BagModal({ showOnly }: { showOnly?: boolean }) {
  return (
    <BagInventoryContextProvider>
      <BagModalInner showOnly={showOnly} />
    </BagInventoryContextProvider>
  );
}

//物品栏
function BagInventory({
  setIsDragging,
  isVisible,
  isDragging,
  deleteHighlight,
  onDropToDelete,
  onDragOverToDelete,
  onDragLeaveToDelete,
  bagInventoryList,
  materialList,
  isPending,
  fetchAllData,
}: {
  setIsDragging: Dispatch<SetStateAction<boolean>>;
  isVisible: boolean;
  isDragging: boolean;
  deleteHighlight?: boolean;
  onDropToDelete?: (item: any) => void;
  onDragOverToDelete?: () => void;
  onDragLeaveToDelete?: () => void;
  bagInventoryList: IBagInventoryItem[];
  materialList: any[];
  isPending: boolean;
  fetchAllData: () => void;
}) {
  // 当前选中的标签 默认是物品背包 （物品背包/合成背包）
  const tab = useBagContextSelector((state) => state.tab);
  const scoreConfigRef = useRef<{ [key: string]: number }>({});
  const [isMaterialDataList, setIsMaterialDataList] = useState<any[]>([]);

  // 当弹窗可见时获取数据
  useEffect(() => {
    if (isVisible) {
      fetchAllData();
    }
  }, [isVisible]);

  useEffect(() => {
    if (materialList.length > 0) {
      ConfigManager.getInstance().getData((data) => {
        scoreConfigRef.current = data.material_score_config;
        const newMaterialList = materialList.map((item) => {
          // 获取材料的数量
          // const quantity = item.quantity || 0;
          // 获取材料的tag
          const tag = item.tag || '';
          // 获取材料的积分
          const score = scoreConfigRef.current[tag as keyof typeof scoreConfigRef.current];
          return {
            ...item,
            score,
          };
        });
        setIsMaterialDataList(newMaterialList);
      });
    }
  }, [materialList]);

  return (
    <>
      <BagInventoryView>
        {/* 物品栏列表 */}
        <div className="inventory-list-box">
          {isPending ? (
            <LocalLoading />
          ) : (
            <>
              {/* 物品背包列表 */}
              {tab === TAB_ENUM.THING_BAG && (
                <OutfitList
                  bagInventoryList={bagInventoryList}
                  isVisible={isVisible}
                  setIsDragging={setIsDragging}
                />
              )}

              {/* 材料背包列表 */}
              {tab === TAB_ENUM.MATERIAL_BAG && (
                <MateriaList
                  materialList={isMaterialDataList}
                  isVisible={isVisible}
                  setIsDragging={setIsDragging}
                />
              )}

              {tab === TAB_ENUM.PET_BAG && <PetList isVisible={isVisible} />}
            </>
          )}
        </div>
      </BagInventoryView>
      {/* 物品栏标签切换 */}
      <BagInventoryTabs
        isDragging={isDragging}
        onDropToDelete={onDropToDelete}
        onDragOverToDelete={onDragOverToDelete}
        onDragLeaveToDelete={onDragLeaveToDelete}
        deleteHighlight={deleteHighlight}
      />
    </>
  );
}

interface IBagQuickBarProps {
  isDragging: boolean;
  setIsDragging?: (args: any) => void;
  bagDragItem?: string;
  handleQuickBarDragStart?: (data: IBagInventoryItem & { fromQuickBar: boolean }) => void;
  handleQuickBarDragEnd?: () => void;
  onlyView?: boolean;
  activeShortcut?: string;
  changeCurShortcut?: (key: string) => void;
}

// 快捷栏
export function BagQuickBar({
  isDragging,
  setIsDragging = () => false,
  bagDragItem,
  handleQuickBarDragStart = () => false,
  handleQuickBarDragEnd = () => false,
  onlyView = false,
  activeShortcut = '0',
  changeCurShortcut = () => false,
}: IBagQuickBarProps) {
  /**
   * bagInventoryList： 物品列表数据
   * getBagInventoryListDta： 获取物品列表数据
   * getSyntheticsListData： 获取合成列表数据
   */
  const { bagInventoryList, fetchAllData } = useBagInventory(false);

  // 当前拖拽的快捷栏位置
  const [draggingKey, setDraggingKey] = useState<string>('');
  const refList = useRef<Record<string, HTMLDivElement | null>>({});
  const [, shortcutAnimate] = useAnimate();

  // 修改onSetQuestKey函数，使用fetchAllData替代单独的数据获取
  const onSetQuestKey = (userItemId: string, draggingKey: string, durability?: number) => {
    const loadingToast = toast.loading('Loading...');
    setEquipmentShortcut({
      userItemId: userItemId,
      shortcut: draggingKey,
    })
      .then(async (res) => {
        if (res.data.code === 1) {
          // 使用fetchAllData一次性刷新所有数据
          await fetchAllData();

          // 添加耐久度提示
          if (durability !== undefined) {
            getDurabilityTips(durability);
          }
        } else {
          toast.error(res.data.msg || res.data.message[0], { duration: 6000 });
        }
      })
      .finally(() => {
        toast.dismiss(loadingToast);
      });
  };
  //快捷键到物品对象映射
  const quickListMap = useMemo(() => {
    return bagInventoryList.reduce(
      (acc, cur) => {
        if (cur.shortcut) {
          acc[cur.shortcut] = cur;
        }
        return acc;
      },
      {} as { [key: string]: IBagInventoryItem }
    );
  }, [bagInventoryList]);

  const dragOverItemType = useMemo(() => {
    let safeData = '';
    if (bagDragItem) {
      try {
        safeData = JSON.parse(bagDragItem).type as INVENTORY_TYPE_ENUM;
      } catch (_error) {
        safeData = '';
      }
    }
    return safeData;
  }, [bagDragItem]);

  // 处理拖拽释放事件
  const handleDrop = (e: any) => {
    e.preventDefault(); // 阻止默认行为
    const droppedItem = e.dataTransfer.getData('text/plain');
    try {
      const item: IBagInventoryItem = JSON.parse(droppedItem);
      if (item.type === INVENTORY_TYPE_ENUM.petResource) {
        setDraggingKey('');
        return;
      }
      if (item.itemId) {
        onSetQuestKey(item.userItemId, draggingKey, item.currentDurability);
      }
    } catch (e) {}
    setDraggingKey(''); // 拖拽结束，隐藏边框
  };

  const handleDragOver = (e: any, key: string) => {
    if (!isDragging) {
      return;
    }
    e.preventDefault();
    if (dragOverItemType === INVENTORY_TYPE_ENUM.petResource) {
      e.dataTransfer.dropEffect = 'none';

      return;
    }
    setDraggingKey(key);
  };

  useEffect(() => {
    const changeAnimate = async () => {
      const el = refList.current[activeShortcut];
      const item = quickListMap[activeShortcut]?.icon;
      if (el && item) {
        await shortcutAnimate(
          el,
          {
            y: '-2rem',
          },
          {
            duration: 0.2,
            ease: 'backOut',
            stiffness: 200,
            damping: 10,
          }
        );
        await shortcutAnimate(
          el,
          {
            y: 0,
          },
          {
            duration: 0.2,
            ease: 'backOut',
            stiffness: 100,
            damping: 0,
          }
        );
      }
    };
    changeAnimate();
  }, [activeShortcut]);

  return (
    <BagQuickBarView>
      {/* 快捷键物品栏组件：显示图标，数量，快捷键等信息，支持拖拽，显示物品详情，支持禁用状态，支持'新物品'标记 */}
      {['1', '2', '3', '4', '5'].map((key, index) => (
        <motion.div
          key={key}
          initial={{ opacity: 0, scale: 0.7 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.7 }}
          transition={{
            delay: index * 0.05,
            duration: 0.5,
            ease: 'backOut',
            scale: {
              type: 'spring',
              stiffness: 400,
              damping: 10,
            },
          }}
          ref={(el) => {
            refList.current[key] = el;
          }}>
          <ContentItem
            isGlowWeapon={
              quickListMap[key]?.currentDurability > 0 && (quickListMap[key] as any)?.isGlowWeapon
            }
            currentDurability={quickListMap[key]?.currentDurability}
            key={key}
            onClick={() => {
              changeCurShortcut(key);
            }}
            src={
              quickListMap[key]?.currentDurability > 0 && quickListMap[key]
                ? quickListMap[key].icon
                : undefined
            }
            // 快捷键
            questKey={key}
            // 拖拽覆盖
            onDragOver={(e: any) => {
              handleDragOver(e, key);
            }}
            // 拖拽离开
            onDragLeave={(e: any) => {
              setDraggingKey('');
            }}
            // 是否可拖拽
            draggable={
              !onlyView && dragOverItemType !== INVENTORY_TYPE_ENUM.pet && !!quickListMap[key]
            }
            // 拖拽开始
            onDragStart={(e: any) => {
              if (quickListMap[key]) {
                setIsDragging(true);
                // 在拖拽数据中添加标记，表明这是从快捷栏拖出的物品
                // 这将帮助背包区分是普通拖拽还是从快捷栏拖拽
                const itemData = {
                  ...quickListMap[key],
                  fromQuickBar: true,
                };
                e.dataTransfer.setData('text/plain', JSON.stringify(itemData));
                handleQuickBarDragStart(itemData);
              }
            }}
            // 拖拽结束
            onDragEnd={(e: any) => {
              setIsDragging(false);
              e.dataTransfer.clearData();
              handleQuickBarDragEnd();
            }}
            // 拖拽释放
            onDrop={handleDrop}
            // 是否选中/高亮
            check={dragOverItemType === INVENTORY_TYPE_ENUM.equipment && draggingKey === key}
            // 鼠标样式
            // style={{
            //   cursor: quickListMap[key] ? 'pointer' : 'default',
            // }}
            style={{
              cursor:
                dragOverItemType === INVENTORY_TYPE_ENUM.petResource
                  ? 'not-allowed'
                  : quickListMap[key]
                    ? 'pointer'
                    : 'default',
            }}
          />
        </motion.div>
      ))}
    </BagQuickBarView>
  );
}
