import { ZoomInDownAnimation } from '@/animates';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

const VictoryContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 30.75rem;
  height: 20rem;
`;

interface VictoryProps {
  isVisible: boolean;
  onComplete?: () => void;
  victoryImage?: string;
}

const Victory = ({ isVisible, onComplete, victoryImage = '/image/victory.webp' }: VictoryProps) => {
  const [isShowing, setIsShowing] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsShowing(true);
      // 2秒后隐藏
      const timer = setTimeout(() => {
        setIsShowing(false);
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  if (!isShowing) return null;

  return (
    <VictoryContainer>
      <ZoomInDownAnimation>
        <Image
          src={victoryImage}
          alt="胜利"
          width={520}
          height={360}
          style={{
            width: '32.5rem',
            height: '22.5rem',
          }}
        />
      </ZoomInDownAnimation>
    </VictoryContainer>
  );
};

export default Victory;
