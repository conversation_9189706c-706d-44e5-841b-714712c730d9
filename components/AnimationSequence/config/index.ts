import { RewardType } from '@/constant/enum';

export interface AnimationConfigItem {
  victoryImageSrc: string;
  failureImageSrc: string;
  eventImageSrc: string;
  eventImageActiveSrc: string;
}

type AnimationConfig = Record<RewardType, AnimationConfigItem>;

export const AnimationSequenceConfig: AnimationConfig = {
  [RewardType.wangcai]: {
    eventImageActiveSrc: '/image/easterEgg/wangcai-active.webp',
    eventImageSrc: '/image/easterEgg/wangcai.webp',
    failureImageSrc: '/image/easterEgg/wangcai-failed.webp',
    victoryImageSrc: '/image/easterEgg/wangcai-victory.webp',
  },
  [RewardType.TheLonelyBit]: {
    eventImageActiveSrc: '/image/easterEgg/TheLonelyBit-active.webp',
    eventImageSrc: '/image/easterEgg/TheLonelyBit.webp',
    failureImageSrc: '/image/easterEgg/TheLonelyBit-failed.webp',
    victoryImageSrc: '/image/easterEgg/TheLonelyBit-victory.webp',
  },
  [RewardType.potato]: {
    eventImageActiveSrc: '/image/easterEgg/potato-active.webp',
    eventImageSrc: '/image/easterEgg/potato.webp',
    failureImageSrc: '/image/easterEgg/potato-failed.webp',
    victoryImageSrc: '/image/easterEgg/potato-victory.webp',
  },
  [RewardType.sQUAQ___000]: {
    eventImageActiveSrc: '/image/easterEgg/sQUAQ___000-active.webp',
    eventImageSrc: '/image/easterEgg/sQUAQ___000.webp',
    failureImageSrc: '/image/easterEgg/sQUAQ___000-failed.webp',
    victoryImageSrc: '/image/easterEgg/sQUAQ___000-victory.webp',
  },
  [RewardType.sPIZZA___000]: {
    eventImageActiveSrc: '/image/easterEgg/sPIZZA___000-active.webp',
    eventImageSrc: '/image/easterEgg/sPIZZA___000.webp',
    failureImageSrc: '/image/easterEgg/sPIZZA___000-failed.webp',
    victoryImageSrc: '/image/easterEgg/sPIZZA___000-victory.webp',
  },
};
