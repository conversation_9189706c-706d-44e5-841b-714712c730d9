import Dialog from '@/commons/Dialog';
import { forwardRef, useImperativeHandle, useState } from 'react';
import styled from 'styled-components';
import rewardBg from '/public/image/reward-bg.webp';
import rewardTitle from '/public/image/surprise.webp';
import Image from 'next/image';
import StartButton from '@/components/EventRules/components/StartButton';
import Star1 from '/public/image/star1.svg';
import Star2 from '/public/image/star2.svg';
import TaskFbIcon from '/public/image/task/fb.png';

export interface RewardsProps {
  onCloseCallback?: () => void;
  onClaimReward?: () => void;
}

export interface SurpriseRewardRef {
  open: (rewardInfo: RewardInfo) => void;
  close: () => void;
  setLoading: (loading: boolean) => void;
}

interface RewardInfo {
  quantity: string;
  name: string;
}

const Container = styled.div`
  width: 46.5rem;
  height: 27.5rem;
  background-image: url(${rewardBg.src});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  // 定义一个伪类affter， 这个元素用于弹窗背景，需要绘制一些淡黄色的朦胧感，主要是围绕着整个父元素，层级要最低

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 169, 69, 0.9); /* 淡黄色半透明背景 */
    filter: blur(1.25rem); /* 添加模糊效果增加朦胧感 */
    z-index: -1; /* 确保在底层 */
    border-radius: 2.5rem;
    pointer-events: none; /* 确保不影响交互 */
    transform: translateZ(0);
  }

  .reward-title {
    width: 24.75rem;
    height: 5.5rem;
    background-image: url(${rewardTitle.src});
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    top: -2%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background-position: bottom;
  }

  .rewards-content {
    width: 100%;
    height: calc(100% - 6.25rem);
    display: flex;
    align-items: center;
    justify-content: start;
    padding-top: 10%;
    flex-direction: column;
    gap: 0.375rem;

    & > p {
      color: #140f08;
      margin: 0;
      padding: 0;
    }

    .rewards-content-item {
      font-size: 1.25rem;
      word-wrap: break-word;
      width: 100%;
      text-align: center;
      color: #140f08;
      font-weight: 900;
      text-shadow: 0 0 0.03125rem #000;
      -webkit-text-stroke: 0.03125rem #000;
    }

    .following {
      text-shadow: none;
      width: 100%;
      -webkit-text-stroke: 0rem;
    }

    .pizza-swap {
      color: #000;
      font-weight: 900;
      text-shadow: 0 0 0.03125rem #000;
    }
  }

  .star1 {
    position: absolute;
    top: 10%;
    left: -4%;
  }

  .star2 {
    position: absolute;
    top: 25%;
    left: -6%;
  }

  .group-jinbi {
    position: absolute;
    bottom: -5%;
    right: -8%;
    background: url('/image/start-group.webp');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    width: 10.375rem;
    height: 12rem;
  }
`;

const RewardNumber = styled.div`
  /* border-radius: 1.875rem; */
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.25rem 0rem;
  box-sizing: border-box;
  width: 80%;
  height: 9.375rem;
  /* 更强烈边框内凹立体效果 */
  /* box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15); */

  /* 使用渐变背景增强立体感 */
  /* background-color: #f7e7cd; */

  /* 去除所有边框 */
  border: none;
  outline: none;
  gap: 0.625rem;
`;

const Items = styled.div`
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 0.625rem;
  border-top: 0.1875rem solid #8c8475;
  box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
  background: #c2b8a2;
  border-radius: 1rem;
  /* width: 6.25rem; */
  height: 2.1875rem;
  position: relative;

  .icon {
    .icon-image {
      border-radius: 50%;
      left: -0.625rem;
      position: relative;
    }
  }

  .value {
    color: #fff;
    font-size: 1.25rem;
    font-weight: bold;
    /* padding-left: 0.625rem; */
    padding-right: 0.625rem;
  }
`;

const publicUrl = 'https://fractal-static.unisat.io/icon/brc20/';

const SurpriseReward = forwardRef<SurpriseRewardRef, RewardsProps>((props, ref) => {
  const { onCloseCallback, onClaimReward } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rewardInfo, setRewardInfo] = useState<RewardInfo>({
    quantity: '',
    name: '',
  });

  useImperativeHandle(ref, () => ({
    open: (info: RewardInfo) => {
      setRewardInfo(info);
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
    setLoading: (loading: boolean) => setIsLoading(loading),
  }));

  const onClaim = () => {
    // window.open(
    //   `https://x.com/intent/retweet?tweet_id=1930926465186677192`,
    //   '_blank',
    //   'width=600,height=600'
    // );
    setIsOpen(false);
    onCloseCallback?.();
  };

  const tickIcon = rewardInfo.name === 'sFB___000' ? TaskFbIcon.src : publicUrl + rewardInfo.name;

  return (
    <Dialog
      isOpen={isOpen}
      onClose={() => {
        setIsOpen(false);
        onCloseCallback?.();
      }}>
      <Container>
        <div className="reward-title"></div>
        <Image
          src={Star1}
          alt="star1"
          className="star1"
          width={60}
          height={60}
          style={{
            width: '3.75rem',
            height: '3.75rem',
          }}
        />
        <Image
          src={Star2}
          alt="star2"
          className="star2"
          width={34}
          height={34}
          style={{
            width: '2.125rem',
            height: '2.125rem',
          }}
        />
        <div className="rewards-content">
          <p className="rewards-content-item">Congratulations!</p>
          <p className="rewards-content-item">You’ve unlocked a hidden reward.</p>

          <p className="rewards-content-item following">
            Please confirm your reward on <span className="pizza-swap">PizzaSwap:</span>
          </p>
          <RewardNumber>
            <Items>
              <div className="icon">
                <Image
                  src={tickIcon}
                  alt=""
                  width={56}
                  height={56}
                  className="icon-image"
                  style={{
                    width: '3.5rem',
                    height: '3.5rem',
                  }}
                />
              </div>
              <div className="value">{rewardInfo.quantity}</div>
            </Items>
          </RewardNumber>
          <StartButton text="Claim" onClick={onClaim} loading={isLoading} />
        </div>
        <div className="group-jinbi" />
      </Container>
    </Dialog>
  );
});

SurpriseReward.displayName = 'SurpriseReward';

export { SurpriseReward };
