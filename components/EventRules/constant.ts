import { px2rem } from '@/utils/px2rem';

export const ruleImageMap = {
  orderTree: {
    popupImages: [
      {
        src: '/image/easterEgg/orderTree_step1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Craft axes from the NPC to start chopping trees.',
      },
      {
        src: '/image/easterEgg/orderTree_step2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description: 'Find glowing trees and chop them in the right order.',
      },
      {
        src: '/image/easterEgg/easterRule_step3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Complete the challenge to earn token rewards!',
      },
    ],
    titleBgSrc: '/image/easterEgg/orderTree_title.webp',
    bgImgSrc: '/image/easterEgg/orderTree_bg.webp',
    preload: true,
  },
  petBed: {
    popupImages: [
      {
        src: '/image/pet/petShedDesc_1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Collect resources and craft a Pet Nest via NPC.',
      },
      {
        src: '/image/pet/petShedDesc_2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description: 'Pet Nest will be added to your backpack.',
      },
      {
        src: '/image/pet/petShedDesc_3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Place the Pet Nest on the map to attract pets.',
      },
    ],
    titleBgSrc: '/image/petBed_title.webp',
    bgImgSrc: '/image/petDesc_bg.webp',
    preload: true,
  },
  petRule: {
    popupImages: [
      {
        src: '/image/pet/petDesc_1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Pet Nest will attract pets with different attributes.',
      },
      {
        src: '/image/pet/petDesc_2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description: 'Pets can work for you, automatically collecting resources from the map.',
      },
      {
        src: '/image/pet/petDesc_3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Use the “Fusion Device” to merge pets into higher levels.',
      },
    ],
    titleBgSrc: '/image/petDesc_title.webp',
    bgImgSrc: '/image/petDesc_bg.webp',
    preload: true,
  },
  pizzaRule: {
    popupImages: [
      {
        src: '/image/easterEgg/pizzaRule_step1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description:
          'Pick up the pizza boxes and put them in your backpack before the countdown ends.',
      },
      {
        src: '/image/easterEgg/pizzaRule_step2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description:
          'Use your skills to choose the best route and collect as many pizza boxes as possible.',
      },
      {
        src: '/image/easterEgg/easterRule_step3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: `The more pizza boxes you collect, the more tokens you’ll earn.`,
      },
    ],
    titleBgSrc: '/image/easterEgg/pizzaRush_title.webp',
    bgImgSrc: '/image/easterEgg/pizzaRush_bg.webp',
    preload: false,
  },
  whackAMole: {
    popupImages: [
      {
        src: '/image/easterEgg/whackAMole_step1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Tap ‘Start’ to begin Tap & Stick Fun.',
      },
      {
        src: '/image/easterEgg/whackAMole_step2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description: 'Quickly tap the stickers in order.',
      },
      {
        src: '/image/easterEgg/easterRule_step3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Complete the challenge to earn token rewards!',
      },
    ],
    titleBgSrc: '/image/easterEgg/whackAMole_title.webp',
    bgImgSrc: '/image/easterEgg/whackAMole_bg.webp',
    preload: true,
  },
  playerEnergy: {
    popupImages: [
      {
        src: '/image/energy_step1.webp',
        imgObj: '',
        alt: 'Popup image 1',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Collecting resources consumes Energy',
      },
      {
        src: '/image/energy_step2.webp',
        imgObj: '',
        alt: 'Popup image 2',
        width: px2rem(252),
        height: px2rem(252),
        className: 'tep2',
        description: 'At 0 Energy, enter a 30 seconds Weak state',
      },
      {
        src: '/image/energy_step3.webp',
        imgObj: 'tep3',
        alt: 'Popup image 3',
        width: px2rem(252),
        height: px2rem(252),
        className: '',
        description: 'Energy fully restores daily at UTC+8',
      },
    ],
    titleBgSrc: '',
    bgImgSrc: '',
    preload: false,
  },
};
