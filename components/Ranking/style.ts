import styled from 'styled-components';

export const RankingTableContainer = styled.div`
  cursor: pointer;
  position: relative;
`;

export const RankingTabsContainer = styled.div`
  position: absolute;
  left: 60%;
  top: 0%;
  transform: translate(-50%, 0);
  z-index: 4;
`;

export const RankingTableBtnView = styled.div`
  /* padding: 0.8em 1.7em; */
  /* background-color: #fc7922; */
  /* border-radius: 30px; */
  background-image: url('/image/community.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: 0.5s;
  min-width: 15.4375rem;
  min-height: 4rem;
  /* 初始状态下边框透明 */
  /* border: 4px solid #fff; */
  /* 添加边框过渡效果 */
  transition: border-color 0.8s ease;
  /* color: #fff; */
  z-index: 1;
  /* width: 247px;
  height: 76px; */

  .axe2 {
    position: absolute;
    left: 1rem;
    top: 0.125rem;
    z-index: 3;
  }

  .event {
    position: absolute;
    left: 50%;
    top: 0%;
    z-index: 3;
    transform: translate(-50%, 0.3125rem);
    width: 100%;
    text-align: center;
    font-size: 1.5rem; /* 更大的字体 */
    font-weight: 900; /* 更粗的字体 */
    color: #ffffff; /* 纯白色 */
    /* 多层文字阴影创建粗描边效果 */
    text-shadow: 
      /* 轻微的全方位描边 */
      -0.0625rem -0.0625rem 0 rgba(0, 0, 0, 0.4),
      0.0625rem -0.0625rem 0 rgba(0, 0, 0, 0.4),
      -0.0625rem 0.0625rem 0 rgba(0, 0, 0, 0.4),
      0.0625rem 0.0625rem 0 rgba(0, 0, 0, 0.4),
      /* 底部明显的阴影 */ 0 0.125rem 0 rgba(0, 0, 0, 0.7),
      0 0.1875rem 0.25rem rgba(0, 0, 0, 0.5);
    background: linear-gradient(to bottom, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    background-clip: text;

    /* 可选：添加适当的过渡效果 */
    transition: transform 0.2s ease;
  }

  .axe {
    position: absolute;
    left: 0.125rem;
    bottom: -0.4375rem;
  }
  .tree {
    position: absolute;
    right: 0px;
    bottom: -0.4375rem;
  }
  .swing {
    position: absolute;
    left: 50%;
    bottom: 0.625rem;
    transform: translate(-50%, 0);
    /* width: calc(100% - 20px); */
    /* height: 20px; */
    z-index: 3;
  }
`;

// &:hover {
//   color: rgb(10, 25, 30);
// }

// &::before,
// &::after {
//   content: "";
//   display: block;
//   width: 10px;
//   height: 10px;
//   transform: translate(-50%, -50%);
//   position: absolute;
//   border-radius: 50%;
//   z-index: -1;
// }

// &::before {
//   top: 0rem;
//   left: -30px;
//   background-color: #13906d;
//   /* hover时慢速扩散，离开时快速收缩 */
//   transition: all 0.7s ease-in, all 0.4s ease-out;
//   z-index: -1;
// }

// &::after {
//   top: 0rem;
//   left: -30px;
//   background-color: #d8c83c;
//   transition: all 0.3s ease-in, all 0.8s ease-out;
//   z-index: -2;
// }

// &:hover::before {
//   width: 260%;
//   height: 400%;
//   transition-delay: 0.3s;
//   transition-timing-function: ease-in;
//   background-color: #13906d;
// }

// &:hover::after {
//   width: 260%;
//   height: 400%;
//   transition-timing-function: ease-in-out;
//   background-color: #d8c83c;
// }

// &:hover {
//   color: rgb(10, 25, 30);
// }

// &:active {
//   filter: brightness(0.8);
// }
