import Dialog from '@/commons/Dialog';
import { useMemo, useState } from 'react';
import styled from 'styled-components';
import rewardBg from '/public/image/reward-bg.webp';
import rewardTitle from '/public/image/rewardTitle.webp';
// import rewardAxe1 from "/public/image/rewardsAxe-1.png";
import rewardAxe2 from '/public/image/rewards-axe-2.webp';
// import potato from "/public/image/wangcai.png";
import Image from 'next/image';
import StartButton from '../EventRules/components/StartButton';
import Star1 from '/public/image/star1.svg';
import Star2 from '/public/image/star2.svg';
import { ShakeYAnimation } from '@/animates';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import icon_wangcai from '/public/image/coin/icon_wangcai.png';
import potato_icon from '/public/image/coin/potato_icon.png';
import sPizza___000 from '/public/image/coin/sPizza___000.png';
import sQUAQ___000 from '/public/image/coin/sQUAQ___000.png';
import TheLonelyBit from '/public/image/coin/TheLonelyBit.png';
import sFB___000 from '/public/image/coin/sFB___000.png';

// const BILLION = 100;

const COMMUNITY_ICON_MAP = {
  potato: potato_icon.src,
  wangcai: icon_wangcai.src,
  sPizza___000: sPizza___000.src,
  sQUAQ___000: sQUAQ___000.src,
  TheLonelyBit: TheLonelyBit.src,
  sFB___000: sFB___000.src,
};

type CommunityType = keyof typeof COMMUNITY_ICON_MAP;

interface RewardsProps {
  onCloseCallback?: () => void;
}

const Container = styled.div`
  width: 46.5rem;
  height: 27.5rem;
  background-image: url(${rewardBg.src});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 169, 69, 0.9); /* 淡黄色半透明背景 */
    filter: blur(1.25rem); /* 添加模糊效果增加朦胧感 */
    z-index: -1; /* 确保在底层 */
    border-radius: 2.5rem;
    pointer-events: none; /* 确保不影响交互 */
    transform: translateZ(0);
  }
  .reward-title {
    width: 34rem;
    height: 7.5rem;
    background-image: url(${rewardTitle.src});
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    top: -6%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
    z-index: 10;
    isolation: isolate; // 创建心的堆叠上下文
    background-color: transparent;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 0.5rem solid rgba(236, 169, 69, 0.9); /* 淡黄色边框 */
      box-shadow: 0 0 0.9375rem rgba(236, 169, 69, 0.9); /* 发光效果 */
      border-radius: 2.5rem;
      z-index: -10;
      pointer-events: none;
      background: transparent; /* 确保内部透明 */
      box-sizing: border-box;
      filter: blur(1.25rem);
    }
    & > p {
      margin: 0;
      padding: 0;
    }
    .reward-title-text {
      font-size: 2.5rem;
      font-weight: 700;
      color: #fff;
      text-align: center;
      text-shadow: 0rem 0.3125rem 0.625rem rgba(0, 0, 0, 0.7);
      width: 100%;
    }
    .reward-title-text-2 {
      background-image: url(${rewardAxe2.src});
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      width: 19.9375rem;
      height: 2.5rem;
      margin: 0 auto;
    }
  }

  .rewards-content {
    width: 100%;
    height: calc(100% - 6.25rem);
    display: flex;
    align-items: center;
    justify-content: start;
    padding-top: 10%;
    flex-direction: column;
    gap: 0.375rem;
    & > p {
      color: #140f08;
      margin: 0;
      padding: 0;
    }
    .rewards-content-item {
      font-size: 1.25rem;
      word-wrap: break-word;
      width: 100%;
      text-align: center;
      color: #140f08;
      font-weight: 900;
      text-shadow: 0 0 0.03125rem #000;
      -webkit-text-stroke: 0.03125rem #000;
    }
    .order {
      font-size: 1.5rem;
      font-weight: 900;
      color: #ff8316;
    }
    .following {
      text-shadow: none;
      width: 100%;
      -webkit-text-stroke: 0rem;
      .pizzaswap {
        font-weight: 900;
        text-shadow: 0 0 0.03125rem #000;
        -webkit-text-stroke: 0.03125rem #000;
      }
    }
  }
  .star1 {
    position: absolute;
    top: 10%;
    left: -4%;
  }
  .star2 {
    position: absolute;
    top: 25%;
    left: -6%;
  }
  .group-jinbi {
    position: absolute;
    bottom: -5%;
    right: -8%;
    background: url('/image/jinbi-group.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 10.375rem;
    height: 12rem;
  }
`;
const RewardNumber = styled.div`
  font-size: 1.125rem;
  font-weight: bold;
  color: #fff;
  /* width: 5.625rem; */
  max-width: 100%;
  height: 2.5rem;
  border-radius: 0.625rem;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2.5rem 0rem;
  padding: 0 0.875rem;
  width: fit-content;

  /* 更强烈的上边框立体效果 */
  box-shadow:
    inset 0rem 0.3125rem 0.25rem 0rem rgba(140, 132, 117, 0.7),
    0rem 0.5rem 0.25rem 0rem rgba(0, 0, 0, 0.1);

  /* 使用渐变背景增强立体感 */
  background-color: #c2b8a2;

  /* 去除所有边框 */
  border: none;
  outline: none;

  .jinbi {
    position: absolute;
    top: 50%;
    left: 0%;
    /* width: 2.5rem; */
    /* height: 2.5rem; */
    transform: translate(-50%, -50%);
  }

  .number {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    left: 0.625rem;
    color: #fff;
    font-size: 1.25rem;
    margin-left: 0.625rem;
  }
`;

// const TWEET_ID = "1908177688449400941";
function useRewards(props: RewardsProps = {}) {
  const [isOpen, setIsOpen] = useState(false);
  const [quantity, setQuantity] = useState<number | string>('');
  const { randomEventResult } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const onShare = () => {
    setIsOpen(false);
    setQuantity('');
    props.onCloseCallback?.();
  };
  const handleClose = () => {
    setIsOpen(false);
    props.onCloseCallback?.(); // 调用传入的回调函数
  };

  const communityIcon = useMemo(() => {
    if (randomEventResult) {
      const { tag } = randomEventResult;
      if (tag) {
        return COMMUNITY_ICON_MAP[tag as CommunityType];
      }
    }
    return '';
  }, [randomEventResult]);

  // TODO

  const Rewards = useMemo(() => {
    const RewardsModal = () => (
      <Dialog isOpen={isOpen} onClose={handleClose}>
        <Container>
          <div className="reward-title">
            <p className="reward-title-text">Surprise Rewards</p>
            <p className="reward-title-text-2" />
          </div>
          <Image
            src={Star1}
            alt="star1"
            className="star1"
            width={60}
            height={60}
            style={{
              width: '3.75rem',
              height: '3.75rem',
            }}
          />
          <Image
            src={Star2}
            alt="star2"
            className="star2"
            width={34}
            height={34}
            style={{
              width: '2.125rem',
              height: '2.125rem',
            }}
          />
          <div className="rewards-content">
            <p className="rewards-content-item">Congratulations!</p>
            <p className="rewards-content-item">You’ve unlocked a hidden reward.</p>

            <p className="rewards-content-item following">
              You can claim the following reward on <span className="pizzaswap">Pizzaswap:</span>
            </p>
            <RewardNumber>
              {communityIcon && (
                <Image
                  src={communityIcon}
                  alt=""
                  className="jinbi"
                  width={48}
                  height={48}
                  style={{
                    width: '3rem',
                    height: '3rem',
                  }}
                />
              )}
              <span className="number">{randomEventResult?.quantity || ''}</span>
            </RewardNumber>
            <ShakeYAnimation duration="1s">
              <StartButton text="Claim" onClick={onShare} />
            </ShakeYAnimation>
          </div>
          <div className="group-jinbi" />
        </Container>
      </Dialog>
    );
    return RewardsModal;
  }, [isOpen]);

  return { Rewards, isOpen, setIsOpen, setQuantity };
}

export default useRewards;
