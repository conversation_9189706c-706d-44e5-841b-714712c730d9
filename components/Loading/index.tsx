'use client';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { IAppState } from '../../constant/type';
import Progress from './Progress';
// import styled from "styled-components";
import LoadingGif from '/public/image/loading.gif';
// import dynamic from "next/dynamic";
import { LoadingPageType } from '@/game/Config/DoorConfig';
import MachineLoading from '@/components/Machine';
import { IS_MOBILE_ENV } from '@/constant';

// const MachineLoading = dynamic(() => import("@/components/Machine"), {
//   ssr: false,
// });

// const LoadingContainer = styled.div`
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100vw;
//   height: 100vh;
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
//   background: radial-gradient(circle, #fbd0ac, #f7ac6b);
//   backdrop-filter: blur(1.5rem);
//   z-index: 1000;
// `;

// const LoadingImage = styled.img`
//   width: 7.5rem;
// `;

// const LoadingText = styled.p`
//   color: #000000;
//   font-size: 1.5rem;
//   font-family: JetBrainsMono;
//   margin-top: 0;
// `;

export default function Loading() {
  const router = useRouter();
  const { pageLoadingRate, isRunJumpLogic, loaderType } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [progress, setProgress] = useState(0);
  const interval: any = useRef(null);

  const needLoading = useMemo(() => {
    if (['/', '/avatar'].includes(router.pathname)) {
      return true;
    }
    return false;
  }, [router]);

  useEffect(() => {
    if (interval.current) {
      clearInterval(interval.current);
    }
    if (!needLoading || !isRunJumpLogic) {
      return () => clearInterval(interval.current);
    }
    if (pageLoadingRate === 0) {
      setProgress(0);
      // Progress increments gradually until 90
      interval.current = setInterval(() => {
        setProgress((prev) => {
          const nextProgress = prev + 1;
          return nextProgress < 90 ? nextProgress : 90;
        });
      }, 20); // Adjust interval speed as needed
    } else if (pageLoadingRate === 100) {
      // Progress quickly increments to 100
      interval.current = setInterval(() => {
        setProgress((p) => {
          if (p === 100) {
            clearInterval(interval.current);
          }
          return p < 100 ? p + 1 : 100;
        });
      }, 20); // Faster increment to smoothly transition to 100
    }

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, [pageLoadingRate, needLoading, isRunJumpLogic]);

  if (!needLoading || (pageLoadingRate === 100 && progress === 100)) {
    return null;
  }
  // 根据Redux状态选择加载器类型
  switch (loaderType) {
    case LoadingPageType.Machine:
      if (!IS_MOBILE_ENV) {
        return (
          <MachineLoading>
            <Progress progress={progress} />
          </MachineLoading>
        );
      }
    case LoadingPageType.Default:
    default:
      return (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            background: 'radial-gradient(circle, #fbd0ac, #f7ac6b)',
            backdropFilter: 'blur(1.5rem)',
            zIndex: 1001,
          }}>
          <img src={LoadingGif.src} alt="" style={{ width: '7.5rem' }} draggable={false} />
          <p
            style={{
              color: '#000000',
              fontSize: '1.5rem',
              fontFamily: 'JetBrains Mono',
              marginTop: 0,
            }}>
            Loading...
          </p>
          <Progress progress={progress} />
        </div>
      );
  }
}
