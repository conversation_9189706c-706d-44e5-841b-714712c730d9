import { PetStatus } from '@/constant/enum';

const colorConfigArr = [
  {
    borderColor: '#006F9E',
    color: '#8EDCFF',
  },
  {
    borderColor: '#FF8316',
    color: '#FFD468',
  },
  {
    borderColor: '#027D14',
    color: '#8EFFC7',
  },
  {
    borderColor: '#F23838',
    color: '#FFA268',
  },
];

const _unSupportOperation = [
  {
    name: 'Fight',
    iconId: 'petFollow',
    action: PetStatus.BATTLE,
    panelColorConfig: colorConfigArr[3],
    panelDisplay: 'Fighting',
  },
];
export const bagOnlyStatusConfig = [
  {
    name: 'Rest',
    iconId: 'standBy',
    action: PetStatus.REST,
    panelColorConfig: colorConfigArr[2],
    panelDisplay: 'Resting',
  },
];

export const basicOperationArr = [
  {
    name: 'Follow',
    iconId: 'petFollow',
    action: PetStatus.FOLLOW,
    panelColorConfig: colorConfigArr[0],
    panelDisplay: 'Following',
  },
  {
    name: 'Recover',
    iconId: 'spa',
    action: PetStatus.IDLE,
    panelColorConfig: colorConfigArr[2],
    panelDisplay: 'Recovering',
  },
];

export const rideOperationConfig = {
  name: 'Ride',
  iconId: 'ride',
  action: PetStatus.RIDE,
  panelColorConfig: colorConfigArr[0],
  panelDisplay: 'Riding',
};

export const featureOperationConfigArr = [
  {
    name: 'Chopping',
    iconId: 'featureAxe',
    action: PetStatus.AXE,
    panelColorConfig: colorConfigArr[1],
    panelDisplay: 'Chopping',
  },
  {
    name: 'Mining',
    iconId: 'featurePickAxe',
    action: PetStatus.PICKAXE,
    panelColorConfig: colorConfigArr[1],
    panelDisplay: 'Mining',
  },
  {
    name: 'Fishing',
    iconId: 'featureFish',
    action: PetStatus.FISHING_POLE,
    panelColorConfig: colorConfigArr[1],
    panelDisplay: 'Fishing',
  },
];

export const staminaColorConfigArr = [
  {
    color: '#2FF4A5',
    limit: 80,
  },
  {
    color: '#DDF42F',
    limit: 50,
  },
  {
    color: '#FF8316',
    limit: 10,
  },
  {
    color: '#FF2720',
    limit: 0,
  },
];

export const statusIconArr = [
  ...basicOperationArr,
  ...bagOnlyStatusConfig,
  ...featureOperationConfigArr,
  rideOperationConfig,
];
