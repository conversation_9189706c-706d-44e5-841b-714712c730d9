import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { usePetPanelContextDispatch, usePetPanelContextSelector } from '../../context';
import {
  PetInfoSection,
  PetInfoSectionTitle,
  PetInfoSectionInfoBox,
  PetInfoItem,
  PetInfoItemLabel,
  PetInfoItemValue,
  BtnWrapper,
  PetInfoSectionMainBox,
  ImageBox,
  StatusTag,
  RarityTag,
  RightInfoBox,
  PetPropertyNameWrapper,
  PetNameBox,
  SpMpContainer,
  PetInfoItemValueRate,
  StatusBarContainer,
  StatusBarItem,
  StatusBarInfo,
  StatusBar,
  StyledPreviewStamina,
  StyledCommandListContainer,
} from './style';
import { FC, useMemo, useState } from 'react';
import FeatureTag from './FeatureTag';
import Button from '@/components/Basic/Button';
import styled from 'styled-components';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { PetStatus, Rarity } from '@/constant/enum';
import { ItemConfig } from '@/game/Config/ItemConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import Image from 'next/image';
import { updateModalState } from '@/store/modal';
import { staminaColorConfigArr, statusIconArr } from '../../constant';
import { useChangePetStatus, usePetCommandList } from '../PetStatusPanel';
import useBagInventory from '@/hooks/useBagInventory';
import { AnimatePresence, motion } from 'framer-motion';

const StyledStatusSvg = styled(SvgWrapper)`
  width: 6.5rem;
  height: 1.75rem;
  flex-shrink: 0;
`;

const rarityColorMap = {
  [Rarity.COMMON]: '#989898',
  [Rarity.UNCOMMON]: '#20AE3F',
  [Rarity.RARE]: '#0078E9',
  [Rarity.EPIC]: '#9455EB',
  [Rarity.LEGENDARY]: '#FAB700',
  [Rarity.MYTHIC]: '#FF4516',
};

const StyledAffinityTag = styled(SvgWrapper)`
  width: 1.75rem;
  height: 1.75rem;
`;
const StyledEditSvg = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.375rem;
  background: #a58061;
  cursor: pointer;
  & > svg {
    width: 0.75rem;
    height: 0.75rem;
  }
`;

const MAX_EDIT_PET_NAME_LIMIT = 1;

interface IPetInfoSectionMainProps {
  displayOnly?: boolean;
  className?: string;
  spRecoverView?: {
    previewStamina: number;
  };
}

export const PetInfoSectionMain: FC<IPetInfoSectionMainProps> = ({
  displayOnly = false,
  className = '',
  spRecoverView,
}) => {
  const petId = usePetPanelContextSelector((state) => state.selectedPetId);
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const contextDispatch = usePetPanelContextDispatch();

  const petInfo = useMemo(() => {
    return petList.find((item) => item._id === petId);
  }, [petId, petList]);
  const editTable = (petInfo?.renameCount || 0) < MAX_EDIT_PET_NAME_LIMIT;

  const handleEditClick = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isRenameModalOpen: true,
      },
    });
  };

  const currentOperation =
    statusIconArr.find((item) => item.action === petInfo?.petStatus) || statusIconArr[0];

  const staminaConfig = useMemo(() => {
    if (petInfo) {
      const percent = (petInfo!.currentStamina / petInfo!.stamina) * 100;
      let color = staminaColorConfigArr[0].color;
      for (let index = 0; index < staminaColorConfigArr.length; index++) {
        const item = staminaColorConfigArr[index];
        if (percent >= item.limit) {
          color = item.color;
          break;
        }
      }

      return {
        percent,
        color,
      };
    }
    return {
      percent: 0,
      color: staminaColorConfigArr[0].color,
    };
  }, [petInfo]);

  if (!petInfo) return null;

  return (
    <PetInfoSectionMainBox className={className}>
      <ImageBox $bgSrc={petInfo.bagConfigInfo.infoImageUrl}>
        <StatusTag
          $borderColor={currentOperation.panelColorConfig.borderColor}
          $color={currentOperation.panelColorConfig.color}>
          <StyledStatusSvg>
            <SpriteSvg id="petStatus" />
          </StyledStatusSvg>
          <span>
            <span data-text={currentOperation.panelDisplay}>{currentOperation.panelDisplay}</span>
          </span>
        </StatusTag>
        <RarityTag $color={rarityColorMap[petInfo.quality]}>{petInfo.quality}</RarityTag>
      </ImageBox>
      <RightInfoBox>
        <PetPropertyNameWrapper>
          <StyledAffinityTag>
            <SpriteSvg id={petInfo.affinity} />
          </StyledAffinityTag>
          <PetNameBox>
            <span>{petInfo.bagConfigInfo.name}</span>
            {!displayOnly && editTable && (
              <StyledEditSvg onClick={handleEditClick}>
                <SpriteSvg id="edit" />
              </StyledEditSvg>
            )}
          </PetNameBox>
        </PetPropertyNameWrapper>
        {spRecoverView ? (
          <StatusBarContainer>
            <StatusBarItem>
              <StatusBarInfo>
                <span data-text="HP">HP</span>
                <span>
                  {petInfo.currentStamina}
                  {spRecoverView.previewStamina > 0 && (
                    <StyledPreviewStamina
                      key={'previewStamina'}
                      initial={{ opacity: 0, scale: 0.3 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{
                        opacity: 0,
                        scale: 0.7,
                      }}
                      transition={{
                        duration: 0.4,
                        ease: 'easeOut',
                        scale: {
                          type: 'spring',
                          stiffness: 300,
                          damping: 10,
                        },
                      }}>
                      +{spRecoverView.previewStamina}
                    </StyledPreviewStamina>
                  )}
                  /{petInfo.stamina}
                </span>
              </StatusBarInfo>
              <StatusBar
                $percent={staminaConfig.percent}
                $previewPercent={
                  ((spRecoverView.previewStamina + petInfo.currentStamina) / petInfo.stamina) * 100
                }
                $barColor={staminaConfig.color}
              />
            </StatusBarItem>
          </StatusBarContainer>
        ) : (
          <SpMpContainer>
            <PetInfoItem>
              <PetInfoItemLabel>SP</PetInfoItemLabel>
              <PetInfoItemValueRate>
                <span>{petInfo.currentStamina}</span>
                <span>/{petInfo.stamina}</span>
              </PetInfoItemValueRate>
            </PetInfoItem>
            <PetInfoItem>
              <PetInfoItemLabel>MP</PetInfoItemLabel>
              <PetInfoItemValueRate>
                <span>{petInfo.currentSpirit}</span>
                <span>/{petInfo.spirit}</span>
              </PetInfoItemValueRate>
            </PetInfoItem>
          </SpMpContainer>
        )}
      </RightInfoBox>
    </PetInfoSectionMainBox>
  );
};

const CommandList = () => {
  const petId = usePetPanelContextSelector((state) => state.selectedPetId);
  const petItem = usePetPanelContextSelector((state) => state.selectedPetInfo);
  const handleChangePetStatus = useChangePetStatus(petId);
  const operationConfigArr = usePetCommandList();
  const isChangePetStatusLoading = usePetPanelContextSelector(
    (state) => state.isChangePetStatusLoading
  );
  const contextDispatch = usePetPanelContextDispatch();
  const { getMaterialListData } = useBagInventory(false, false);
  const [hoverIndex, setHoverIdx] = useState(-1);

  const handleFeed = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isPetFeedModalOpen: true,
      },
    });
    getMaterialListData();
  };

  return (
    <StyledCommandListContainer
      onMouseLeave={() => {
        setHoverIdx(-1);
      }}>
      {operationConfigArr.map((item, idx) => {
        const isFeedOperation = item.name === 'Feed';
        return (
          <StyledCommandBtn
            className="commandBtn"
            key={'commandBtn_' + item.name}
            disabled={item.disabled}
            onMouseEnter={() => {
              setHoverIdx(idx);
            }}
            onMouseLeave={() => {
              setHoverIdx(-1);
            }}
            onClick={(e) => {
              e.preventDefault();
              if (item.disabled || isChangePetStatusLoading) return;
              if (isFeedOperation) {
                handleFeed();
                return;
              }
              if (petItem.petStatus !== item.action) {
                handleChangePetStatus(item.action);
              }
            }}>
            <StyledCommandBtnIcon>
              <SpriteSvg id={item.iconId} />
            </StyledCommandBtnIcon>
            <AnimatePresence>
              {hoverIndex === idx && (
                <StyledTips
                  key={'tip_commandBtn_' + item.name}
                  initial={{ opacity: 0, y: '0%' }}
                  animate={{ opacity: 1, y: '-50%' }}
                  exit={{ opacity: 0, y: '-100%' }}
                  transition={{
                    duration: 0.4,
                    ease: 'easeOut',
                  }}>
                  <span>{item.name}</span>
                </StyledTips>
              )}
            </AnimatePresence>
          </StyledCommandBtn>
        );
      })}
    </StyledCommandListContainer>
  );
};

const StyledTips = styled(motion.div)`
  display: flex;
  padding: 0.625rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  position: absolute;
  left: 5.4375rem;
  top: 1.1875rem;
  border-radius: 1.25rem;
  border: 0.0625rem solid #140f08;
  background: #fff;
  box-shadow: 0.1875rem 0.25rem 0 0 rgba(0, 0, 0, 0.25);
  pointer-events: none;
  user-select: none;
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(0%, -50%);
  width: fit-content;
  z-index: 2;
  & > span {
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
  }
`;

const StyledCommandBtnIcon = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  color: white;
`;

const StyledCommandBtn = styled(Button)`
  color: #ffffff;
  display: flex;
  height: 3rem;
  padding: 0.1875rem 0.75rem;
  justify-content: center;
  align-items: center;
  flex: 1;
  width: auto;
  min-width: unset;
  position: relative;
  &:hover {
    z-index: 1;
  }
`;

const PetDetailPanel = () => {
  const petId = usePetPanelContextSelector((state) => state.selectedPetId);
  const contextDispatch = usePetPanelContextDispatch();
  const dispatch = useAppDispatch();
  const handleRelease = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isReleaseModalOpen: true,
      },
    });
  };
  const handleConfirm = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isExpand: false,
      },
    });
    setTimeout(() => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: '',
        },
      });
    }, 300);
  };
  const petInfo = usePetPanelContextSelector((state) => state.selectedPetInfo);
  const handleChangePetStatus = useChangePetStatus(petId);

  // 是否绑定，链上非绑定，非链上绑定
  const isBinding = petInfo?.bindingFlag;

  const foodPreferenceSrc = useMemo(() => {
    let src = '';
    if (petInfo?.foodPreference) {
      ItemConfig.getInstance().getData(Number(petInfo.foodPreference), (data) => {
        if (data) {
          src = getCdnLink(data?.icon_url || '');
        }
      });
    }
    return src;
  }, [petInfo]);
  const openPetDesc = () => {
    dispatch(updateModalState({ petDescModalOpenConfig: { isOpen: true } }));
  };

  const onRecallClick = () => {
    const cb = () => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isExpand: false,
        },
      });
      setTimeout(() => {
        contextDispatch({
          type: 'UPDATE',
          payload: {
            isExpand: false,
            selectedPetId: '',
            selectedPetInfo: {},
          },
        });
      }, 200);
    };
    handleChangePetStatus(PetStatus.REST, cb);
  };

  if (!petInfo) return null;

  return (
    <>
      <PetInfoSectionMain />
      <PetInfoSection>
        <PetInfoSectionTitle>
          <span>Pet Introduction</span>
        </PetInfoSectionTitle>
        <PetInfoSectionInfoBox>
          <PetInfoItem>
            <PetInfoItemLabel>Race:</PetInfoItemLabel>
            <PetInfoItemValue>{petInfo?.species}</PetInfoItemValue>
          </PetInfoItem>
          <PetInfoItem>
            <PetInfoItemLabel>Favorite food:</PetInfoItemLabel>
            <PetInfoItemValue>
              {/* {petInfo?.foodPreference} */}
              {foodPreferenceSrc ? (
                <StyledFoodPrefer>
                  <Image
                    src={foodPreferenceSrc}
                    width={28}
                    height={19}
                    alt="food prefer"
                    draggable={false}
                  />
                </StyledFoodPrefer>
              ) : (
                petInfo?.foodPreference
              )}
            </PetInfoItemValue>
          </PetInfoItem>
          <PetInfoItem>
            <PetInfoItemLabel>Height:</PetInfoItemLabel>
            <PetInfoItemValue>{petInfo?.cm}cm</PetInfoItemValue>
          </PetInfoItem>
          <PetInfoItem>
            <PetInfoItemLabel>Weight:</PetInfoItemLabel>
            <PetInfoItemValue>{petInfo?.kg}kg</PetInfoItemValue>
          </PetInfoItem>
        </PetInfoSectionInfoBox>
      </PetInfoSection>
      <PetInfoSection>
        <PetInfoSectionTitle>
          <span>characteristic</span>
        </PetInfoSectionTitle>
        <PetInfoSectionInfoBox>
          {(petInfo?.featureInfos || []).map((item) => {
            return (
              <FeatureTag
                key={petInfo?._id + item.feature + item.featureLevel}
                feature={item.feature}
                featureLevel={item.featureLevel}
              />
            );
          })}
        </PetInfoSectionInfoBox>
      </PetInfoSection>

      <PetInfoSection>
        <PetInfoSectionTitle>
          <span>Command</span>
        </PetInfoSectionTitle>
        <PetInfoSectionInfoBox>
          <CommandList />
        </PetInfoSectionInfoBox>
      </PetInfoSection>

      <BtnWrapper>
        {isBinding && (
          <StyledButton $type={'cancel'} onClick={handleRelease}>
            <StyledBtnIcon>
              <SpriteSvg id="delete" />
            </StyledBtnIcon>
            release
          </StyledButton>
        )}
        <StyledButton $type={'cancel'} onClick={onRecallClick}>
          <StyledBtnIcon>
            <SpriteSvg id="recall" />
          </StyledBtnIcon>
          recall
        </StyledButton>
        <StyledButton onClick={handleConfirm}>
          <StyledBtnIcon>
            <SpriteSvg id="confirm" />
          </StyledBtnIcon>
          Done
        </StyledButton>
      </BtnWrapper>
      <StyledHelpIcon onClick={openPetDesc}>
        <SpriteSvg id="help" />
      </StyledHelpIcon>
    </>
  );
};

const StyledHelpIcon = styled(SvgWrapper)`
  display: flex;
  width: 2rem;
  height: 2rem;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 0.125rem solid #ff8316;
  background: #fff;
  box-shadow: 0 0.142875rem 0 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
  position: absolute;
  right: 0.8125rem;
  top: 0;
  transform: translateY(-35%);
  z-index: 2;
  & > svg {
    width: 0.875rem;
    height: 1.375rem;
  }
`;

const StyledBtnIcon = styled(SvgWrapper)`
  width: 1rem;
  height: 1rem;
  color: white;
`;

const StyledButton = styled(Button)`
  height: 2.25rem;
  border-radius: 1rem;
  box-sizing: border-box;
  padding: 0.375rem;
  flex: 1;
  width: auto;
  gap: 0.375rem;
  min-width: unset;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: unset;
`;

const StyledFoodPrefer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  & > img {
    width: 1.75rem;
    height: 1.1875rem;
    flex-shrink: 0;
    aspect-ratio: 28/19;
  }
`;

export default PetDetailPanel;
