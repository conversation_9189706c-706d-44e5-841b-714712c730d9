import { useAppSelector } from '@/hooks/useStore';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IBagPetList, SCENE_TYPE } from '@/constant/type';
import { PetStatus } from '@/constant/enum';
import Image from 'next/image';
import {
  AnimatePresence,
  AnimationPlaybackControlsWithThen,
  motion,
  useAnimate,
} from 'framer-motion';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { usePetPanelContextDispatch, usePetPanelContextSelector } from '../../context';
import {
  PetStatusItem,
  PetStatusList,
  PetStatusImageBox,
  StyledCursor,
  PetStamina,
  PetStaminaInner,
  PetImageStaminBox,
  CursorBtnContainer,
  PetOperationList,
  PetOperationItem,
  StyledOperationSvg,
  PetOperationListWrapper,
} from './style';
import { changePetStatus } from '@/server';
import toast from 'react-hot-toast';
import useClickAway from '@/hooks/useClickAway';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import {
  basicOperationArr,
  featureOperationConfigArr,
  rideOperationConfig,
  staminaColorConfigArr,
  statusIconArr,
} from '../../constant';
import { StatusTag } from '../PetDetailPanel/style';
import styled from 'styled-components';
import useBagInventory from '@/hooks/useBagInventory';

export function useChangePetStatus(petId?: string) {
  const isChangePetStatusLoading = usePetPanelContextSelector(
    (state) => state.isChangePetStatusLoading
  );
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const contextDispatch = usePetPanelContextDispatch();

  const handleChangePetStatus = async (petStatus: PetStatus, cb?: () => void) => {
    if (isChangePetStatusLoading || !petId) return;
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isChangePetStatusLoading: true,
      },
    });
    try {
      const res = await changePetStatus({ petId, petStatus });
      if (res.data.code === 1) {
        const newPetData = res.data.data;
        updateGameStatePetItemData(newPetData);
        cb?.();
      } else {
        toast.error(res.data.msg);
      }
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    } catch (error) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    }
  };

  return handleChangePetStatus;
}

export function usePetCommandList() {
  const petItem = usePetPanelContextSelector((state) => state.selectedPetInfo);
  const { sceneType } = useAppSelector((state) => state.AppReducer);

  const isInIsland = sceneType === SCENE_TYPE.Island;

  const feedOperation = [
    {
      name: 'Feed',
      iconId: 'petFeed',
      action: '',
      panelColorConfig: {
        borderColor: '',
        color: '',
      },
      panelDisplay: 'Feed',
    },
  ];

  const operationConfigArr = useMemo(() => {
    const featureList = petItem?.featureInfos?.map((item) => item.feature) || [];
    const isFullStaminal = petItem.stamina === petItem.currentStamina;
    const rideFlag = petItem.rideFlag;

    const list = isInIsland
      ? featureOperationConfigArr.filter((item) => featureList.includes(item.action))
      : [];
    const arr = basicOperationArr.toSpliced(1, 0, ...list);
    if (!isFullStaminal) {
      arr.push(...(feedOperation as any));
    }
    if (rideFlag) {
      arr.push(rideOperationConfig);
    }

    const operationArr = arr.map((item) => {
      const isFeedOperation = item.name === 'Feed';

      const disabled = isFeedOperation
        ? false
        : !isInIsland && ![PetStatus.IDLE, PetStatus.FOLLOW].includes(item.action);

      return { ...item, disabled };
    });

    if (!isInIsland) {
      return operationArr.filter((item) => item.action !== PetStatus.IDLE);
    }

    return operationArr;
  }, [isInIsland, petItem]);

  return operationConfigArr;
}

const OperationList = ({
  petItem,
  closeList = () => false,
}: {
  petItem: IBagPetList;
  closeList: () => void;
}) => {
  const contextDispatch = usePetPanelContextDispatch();
  const isChangePetStatusLoading = usePetPanelContextSelector(
    (state) => state.isChangePetStatusLoading
  );
  const { sceneType } = useAppSelector((state) => state.AppReducer);
  const { getMaterialListData } = useBagInventory(false, false);

  const handleChangePetStatus = useChangePetStatus(petItem._id);
  const isInIsland = sceneType === SCENE_TYPE.Island;
  const operationConfigArr = usePetCommandList();

  const handleFeed = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isPetFeedModalOpen: true,
      },
    });
    getMaterialListData();
    closeList();
  };

  return (
    <>
      {operationConfigArr.map((item) => {
        const isFeedOperation = item.name === 'Feed';

        const disabled = isFeedOperation
          ? false
          : !isInIsland && ![PetStatus.IDLE, PetStatus.FOLLOW].includes(item.action);

        return (
          <PetOperationItem
            key={item.name}
            $active={petItem.petStatus === item.action}
            $loading={isChangePetStatusLoading}
            $disabled={disabled}
            onClick={(e) => {
              e.preventDefault();
              if (disabled) return;
              if (isFeedOperation) {
                handleFeed();
                return;
              }
              if (petItem.petStatus !== item.action) {
                handleChangePetStatus(item.action);
              }
            }}>
            <StyledOperationSvg>
              <SpriteSvg id={item.iconId} />
            </StyledOperationSvg>
            <span>{item.name}</span>
          </PetOperationItem>
        );
      })}
    </>
  );
};

const StyledStatusSvg = styled(SvgWrapper)`
  height: 1.25rem;
  flex-shrink: 0;
`;

const StyledStatusTag = styled(StatusTag)`
  min-width: 4.625rem;
  height: 1.25rem;
  & > span:last-of-type {
    padding: 0 0.5rem;
    & > span {
      font-size: 0.6875rem;
    }
  }
`;

const PetStatusPanel = () => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const currentSummonList = useMemo(() => {
    return petList
      .filter((item) => item.petStatus !== PetStatus.REST)
      .sort((a, b) => (a.lastSummonedAt < b.lastSummonedAt ? -1 : 1));
  }, [petList]);

  const contextDispatch = usePetPanelContextDispatch();
  const selectedPetId = usePetPanelContextSelector((state) => state.selectedPetId);
  const isExpand = usePetPanelContextSelector((state) => state.isExpand);
  const isPetFeedModalOpen = usePetPanelContextSelector((state) => state.isPetFeedModalOpen);

  const handleClick = (item: IBagPetList) => {
    if (isPetFeedModalOpen) return;

    if (isExpand && item._id === selectedPetId) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: '',
          selectedPetInfo: {},
          isExpand: false,
        },
      });
    } else {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: item._id,
          selectedPetInfo: item,
          isExpand: true,
        },
      });
    }
  };

  return (
    <PetStatusList>
      <AnimatePresence>
        {currentSummonList.map((item) => {
          const active = selectedPetId === item._id;
          const percent = (item.currentStamina / item.stamina) * 100;
          const currentStatus = item.petStatus;
          let color = staminaColorConfigArr[0].color;
          for (let index = 0; index < staminaColorConfigArr.length; index++) {
            const item = staminaColorConfigArr[index];
            if (percent >= item.limit) {
              color = item.color;
              break;
            }
          }

          const currentOperation =
            statusIconArr.find((it) => it.action === item.petStatus) || statusIconArr[0];

          const isSelectedItem = item._id === selectedPetId;

          return (
            <PetStatusItem
              key={'petStatus' + item._id}
              initial={{ opacity: 0, scale: 0.3 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.7 }}
              transition={{
                duration: 0.4,
                ease: 'easeOut',
                scale: {
                  type: 'spring',
                  stiffness: 300,
                  damping: 10,
                },
              }}>
              <CursorBtn petItem={item} />
              <PetImageStaminBox
                onClick={(e) => {
                  e.preventDefault();
                  handleClick(item);
                }}
                style={isPetFeedModalOpen && isSelectedItem ? { zIndex: 14 } : {}}>
                <PetStatusImageBox $active={active}>
                  <Image
                    width={512}
                    height={512}
                    src={item.bagConfigInfo.infoImageUrl}
                    loading="lazy"
                    alt="pet image"
                    draggable={false}
                  />
                  <StyledStatusTag
                    $borderColor={currentOperation.panelColorConfig.borderColor}
                    $color={currentOperation.panelColorConfig.color}>
                    <StyledStatusSvg>
                      <SpriteSvg id="petStatus" />
                    </StyledStatusSvg>
                    <span>
                      <span data-text={currentOperation.panelDisplay}>
                        {currentOperation.panelDisplay}
                      </span>
                    </span>
                  </StyledStatusTag>
                </PetStatusImageBox>
                <PetStaminaWrapper percent={percent} color={color} currentStatus={currentStatus} />
              </PetImageStaminBox>
            </PetStatusItem>
          );
        })}
      </AnimatePresence>
    </PetStatusList>
  );
};

const PetStaminaWrapper = ({
  percent,
  color,
  currentStatus,
}: {
  percent: number;
  color: string;
  currentStatus: PetStatus;
}) => {
  // const [, setPercentState] = useState(percent);
  const [showAnimate, setShowAnimate] = useState(false);
  const [ref, shineAnimate] = useAnimate();
  const shineAnimateRef = useRef<AnimationPlaybackControlsWithThen>();

  // useEffect(() => {
  //   setPercentState((prev) => {
  //     const isPlus = percent - prev > 0;
  //     if (isPlus) {
  //       setShowAnimate(true);
  //     }
  //     return percent;
  //   });
  // }, [percent]);

  useEffect(() => {
    if (currentStatus === PetStatus.IDLE) {
      setShowAnimate(true);
    } else {
      setShowAnimate(false);
    }
  }, [currentStatus]);

  useEffect(() => {
    const playAnimate = async () => {
      if (ref.current) {
        shineAnimateRef.current = shineAnimate(
          ref.current,
          {
            opacity: [1, 0.9, 0.8, 0.9, 1],
            filter: [
              // 'brightness(1) saturate(100%)',
              // 'brightness(1.5) saturate(500%)',
              // 'brightness(1.3) saturate(200%)',
              // 'brightness(1.5) saturate(500%)',
              // 'brightness(1) saturate(100%)',

              'brightness(1) saturate(100%)',
              'brightness(0.8) saturate(100%)',
              'brightness(1.1) saturate(200%)',
              'brightness(1.3) saturate(500%)',
              'brightness(1.1) saturate(500%)',
              'brightness(0.8) saturate(100%)',
              'brightness(1) saturate(100%)',
            ],
          },
          {
            duration: 5,
            ease: 'linear',
            repeat: Infinity,
            repeatType: 'loop',
            stiffness: 100,
            damping: 5,
          }
        );
      }
    };
    if (showAnimate) {
      playAnimate();
    } else {
      shineAnimateRef.current?.cancel();
    }
  }, [showAnimate]);

  return (
    <PetStamina>
      <PetStaminaInner
        ref={ref}
        $percent={isNaN(percent) ? 0 : percent}
        style={{ '--percent': isNaN(percent) ? 0 : percent } as any}
        $bgColor={color}></PetStaminaInner>
      <PetStaminaInnerWrapper $percent={isNaN(percent) ? 0 : percent}>
        <AnimatePresence>
          {showAnimate && (
            <StyledSvgWrapper
              initial={{ opacity: 0, y: '0%', x: '-0.5rem' }}
              animate={{
                opacity: 1,
                y: '-35%',
                x: '-0.5rem',
                filter: [
                  'brightness(1)',
                  'brightness(0.8)',
                  'brightness(1.3)',
                  'brightness(1.6)',
                  'brightness(1.3)',
                  'brightness(0.8)',
                  'brightness(1)',
                ],
                transition: {
                  filter: {
                    repeat: Infinity,
                    duration: 5,
                    ease: 'linear',
                    repeatType: 'loop',
                    stiffness: 100,
                    damping: 5,
                  },
                },
              }}
              exit={{
                opacity: 0,
                y: '-60%',
                x: '-0.5rem',
                transition: {
                  duration: 0.2,
                  ease: 'easeOut',
                },
              }}
              transition={{
                duration: 0.4,
                ease: 'easeIn',
              }}>
              <svg viewBox="0 0 33 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M0 10.5C0 4.70101 4.70101 0 10.5 0H17.9274C23.4904 0 28 4.50964 28 10.0726C28 10.3411 28.1285 10.5935 28.3457 10.7514L32.8125 14L28.3457 17.2486C28.1285 17.4065 28 17.6589 28 17.9274C28 23.4904 23.4904 28 17.9274 28H10.5C4.70101 28 0 23.299 0 17.5V10.5Z"
                  fill="#2FF4A5"
                />
                <path
                  d="M10.5 0.4375H17.9277C23.2488 0.437663 27.5623 4.7512 27.5625 10.0723C27.5625 10.4807 27.7576 10.8652 28.0879 11.1055L32.0684 14L28.0879 16.8945C27.7576 17.1348 27.5625 17.5193 27.5625 17.9277C27.5623 23.2488 23.2488 27.5623 17.9277 27.5625H10.5C4.94263 27.5625 0.4375 23.0574 0.4375 17.5V10.5C0.4375 4.94263 4.94263 0.4375 10.5 0.4375Z"
                  stroke="#140F08"
                  strokeWidth="1"
                />
                <path
                  d="M16.8838 6.6377C17.4012 5.991 18.3453 5.88584 18.9922 6.40332C19.5979 6.88828 19.7285 7.74813 19.3164 8.38672L19.2266 8.51172C18.7914 9.0557 18.7999 9.34165 18.8193 9.49219C18.855 9.76768 19.0039 10.0689 19.3574 10.6875C19.5788 11.075 19.9132 11.6419 20.1172 12.3154C20.5202 12.1346 20.999 12.1323 21.4219 12.3516C22.1836 12.7468 22.8331 13.3282 23.3105 14.041C23.788 14.7541 24.0781 15.5767 24.1533 16.4316C24.157 16.474 24.1592 16.5179 24.1592 16.5625L24.1543 16.7539C24.0631 18.7156 22.5777 20.1733 20.9238 21.0645C19.1532 22.0185 16.8094 22.5625 14.3027 22.5625C11.7975 22.5625 9.4543 22.0216 7.68359 21.0713C5.97661 20.1551 4.44557 18.636 4.44531 16.5762L4.45117 16.4502C4.52348 15.5923 4.8112 14.7656 5.28906 14.0488C5.76672 13.3324 6.41828 12.7484 7.18262 12.3516C7.91759 11.9701 8.82321 12.2564 9.20508 12.9912C9.56285 13.6804 9.33367 14.5197 8.69824 14.9365L8.56543 15.0146C8.2898 15.1578 8.04971 15.3596 7.8623 15.6055L7.78516 15.7129C7.60281 15.9864 7.48781 16.2991 7.44824 16.625C7.47341 17.0545 7.84026 17.7513 9.10254 18.4287C10.3565 19.1015 12.1925 19.5625 14.3027 19.5625L14.6943 19.5576C16.6382 19.5042 18.3244 19.0562 19.5 18.4229C20.7592 17.7444 21.1285 17.0468 21.1553 16.6152C21.1144 16.2913 20.9998 15.981 20.8184 15.71C20.6216 15.4162 20.3535 15.1772 20.04 15.0146C20.0195 15.004 20.0003 14.991 19.9805 14.9795C19.8428 15.3061 19.6622 15.6309 19.4326 15.9521L19.2266 16.2256C18.7414 16.832 17.8809 16.9621 17.2422 16.5498L17.1172 16.46C16.4705 15.9421 16.3667 14.9979 16.8838 14.3516C17.3179 13.8088 17.3095 13.5233 17.29 13.3721C17.2545 13.0965 17.1056 12.7947 16.752 12.1758C16.4601 11.665 15.9687 10.8439 15.8438 9.87598C15.7024 8.78007 16.0342 7.69967 16.8838 6.6377ZM13.1299 5.35156C13.6473 4.7053 14.5914 4.59999 15.2383 5.11719C15.8446 5.60225 15.9747 6.46291 15.5625 7.10156L15.4727 7.22656C15.1243 7.66209 15.0177 8.10033 15.0713 8.66016C15.1284 9.25635 15.364 9.9648 15.7109 10.9189C16.2714 12.4603 17.2973 14.9833 15.6416 17.2891L15.4727 17.5117C14.9876 18.1181 14.1278 18.2482 13.4893 17.8359L13.3643 17.7461C12.7175 17.2286 12.6123 16.2846 13.1299 15.6377C13.4782 15.2022 13.5848 14.7641 13.5312 14.2041C13.4741 13.6076 13.2386 12.8987 12.8916 11.9443C12.313 10.3532 11.2383 7.716 13.1299 5.35156ZM9.37598 6.6377C9.89343 5.99099 10.8374 5.88586 11.4844 6.40332C12.0906 6.88833 12.2208 7.74811 11.8086 8.38672L11.7188 8.51172C11.2841 9.0551 11.293 9.34105 11.3125 9.49219C11.3481 9.76768 11.4971 10.0688 11.8506 10.6875C12.1425 11.1983 12.6339 12.0202 12.7588 12.9883C12.9 14.084 12.5681 15.1638 11.7188 16.2256C11.2013 16.8724 10.2573 16.9775 9.61035 16.46C8.96376 15.9425 8.85842 14.9985 9.37598 14.3516C9.81065 13.8082 9.80256 13.5227 9.7832 13.3721C9.74765 13.0965 9.59873 12.7946 9.24512 12.1758C8.95354 11.6655 8.46092 10.8441 8.33594 9.87598C8.19465 8.78016 8.5266 7.69954 9.37598 6.6377Z"
                  fill="white"
                  stroke="#140F08"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </StyledSvgWrapper>
          )}
        </AnimatePresence>
      </PetStaminaInnerWrapper>
    </PetStamina>
  );
};

const PetStaminaInnerWrapper = styled.div<{ $percent: number }>`
  width: 100%;
  bottom: 0;
  position: absolute;
  height: ${({ $percent }) => $percent}%;
`;

const StyledSvgWrapper = styled(motion.span)`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2.0625rem;
  height: 1.75rem;
  position: absolute;
  right: 0;
  top: 0;
  transform: translate(-0.5rem, -35%);
  filter: drop-shadow(0 0.125rem 0 #140f08);
`;

export default PetStatusPanel;

const CursorBtn = ({ petItem }: { petItem: IBagPetList }) => {
  const selectedPetId = usePetPanelContextSelector((state) => state.selectedPetId);
  const contextDispatch = usePetPanelContextDispatch();
  const active = selectedPetId === petItem._id;
  const [showList, setShowList] = useState(false);

  const cursorRef = useRef<HTMLSpanElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const handleCursorClick = (item: IBagPetList) => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        selectedPetId: item._id,
        selectedPetInfo: item,
      },
    });
    setShowList(true);
  };

  useClickAway(() => {
    setShowList(false);
  }, [cursorRef, listRef]);

  const closeList = useCallback(() => {
    setShowList(false);
  }, []);

  return (
    <CursorBtnContainer>
      <StyledCursor
        ref={cursorRef}
        $active={active}
        onClick={(e) => {
          e.preventDefault();
          handleCursorClick(petItem);
        }}>
        <SpriteSvg id="cursor" />
      </StyledCursor>
      <PetOperationListWrapper ref={listRef}>
        <AnimatePresence>
          {active && showList && (
            <PetOperationList
              ref={listRef}
              key={petItem._id + 'operationListAnimate'}
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              exit={{
                opacity: 0,
              }}
              transition={{
                duration: 0.3,
                delay: 0.1,
                ease: 'easeInOut',
              }}>
              <OperationList petItem={petItem} closeList={closeList} />
            </PetOperationList>
          )}
        </AnimatePresence>
      </PetOperationListWrapper>
    </CursorBtnContainer>
  );
};
