import styled, { css } from 'styled-components';
import SvgWrapper from '@/components/SvgWrapper';
import { motion } from 'framer-motion';

export const PetStatusList = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 1.5rem;
  flex-shrink: 0;
`;

export const StyledCursor = styled(SvgWrapper)<{ $active: boolean }>`
  width: 2rem;
  height: 2rem;
  color: ${({ $active }) => ($active ? '#FF8316' : '#140F08')};
  transition:
    color 0.5s ease,
    filter 0.5s ease;
  filter: drop-shadow(0 0.125rem 0 currentColor);
  cursor: pointer;
`;

export const PetStatusImageBox = styled.div<{ $active: boolean }>`
  display: flex;
  width: 6rem;
  height: 6rem;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    bottom: 0.4375rem;
    width: 4.2856875rem;
    height: 4.2856875rem;
    flex-shrink: 0;
    border-radius: 0.857125rem;
    background: ${({ $active }) => ($active ? '#FF8316' : '#FFF')};
    transition: background 0.5s ease;
    z-index: -1;
  }

  & > img {
    width: 6rem;
    height: 6rem;
    flex-shrink: 0;
    aspect-ratio: 1/1;
    border-radius: 1rem;
  }
`;

export const PetStatusItem = styled(motion.div)`
  display: flex;
  width: 8.5rem;
  height: 6rem;
  align-items: flex-end;
  justify-content: center;
  flex-shrink: 0;
  flex-wrap: nowrap;
  &:hover {
    & ${StyledCursor} {
      color: #ff8316;
    }

    & ${PetStatusImageBox} {
      &:hover::before {
        background: #ff8316;
      }
    }
  }
`;

export const PetStamina = styled(motion.div)`
  width: 0.5rem;
  height: 5.5rem;
  background: #cabfab;
  border-radius: 0.25rem;
  position: relative;
`;

export const PetStaminaInner = styled(motion.div)<{ $percent: number; $bgColor: string }>`
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border: solid #140f08;
  box-sizing: border-box;
  border-width: ${({ $percent }) => ($percent > 0 ? '0.0625rem' : '0')};
  border-radius: 0.25rem;
  height: calc(var(--percent) * 1%);
  max-height: 100%;
  background: ${({ $bgColor }) => $bgColor};
  transition:
    height 0.5s ease,
    background 0.5s ease;
  transform-origin: bottom center;
`;

export const PetImageStaminBox = styled.div`
  flex: 1;
  display: flex;
  height: 6rem;
  align-items: flex-end;
  justify-content: center;
  cursor: pointer;
  flex-wrap: nowrap;
`;

export const CursorBtnContainer = styled.div`
  width: 2rem;
  height: 2rem;
  position: relative;
`;

export const PetOperationListWrapper = styled(motion.div)`
  position: absolute;
  top: 100%;
  right: 100%;
  width: auto;
  height: auto;
`;
export const PetOperationList = styled(motion.div)`
  position: absolute;
  top: 100%;
  right: 100%;
  display: flex;
  padding: 1rem;
  align-items: center;
  gap: 0.625rem;
  position: absolute;
  border-radius: 1rem;
  border: 0.125rem solid #ff8316;
  background: #fff;
  box-shadow: 0 0.25rem 0 0 #ff8316;
  flex-direction: column;
`;

export const PetOperationItem = styled.div<{
  $active: boolean;
  $loading: boolean;
  $disabled: boolean;
}>`
  display: flex;
  width: 7.5rem;
  height: 2.5rem;
  padding: 0.1875rem 0.75rem;
  align-items: center;
  gap: 0.25rem;
  border-radius: 1rem;
  background: #fbf4e8;
  color: ${({ $active }) => ($active ? '#ff8316' : '#a58061')};
  font-family: 'JetBrains Mono';
  font-style: normal;
  flex-wrap: nowrap;
  transition:
    color 0.5s ease,
    border-color 0.5s ease;

  cursor: ${({ $loading }) => ($loading ? 'wait' : 'pointer')};

  ${({ $active, $disabled }) =>
    $disabled
      ? css`
          cursor: not-allowed;
        `
      : $active
        ? css`
            color: #ff8316;
            border: 0.0625rem solid #ff8316;
          `
        : css`
            color: #a58061;
            border: 0.0625rem solid transparent;
            &:hover {
              color: #140f08;
            }
          `}

  & > span:last-of-type {
    font-size: 0.875rem;
    font-weight: 400;
  }
`;

export const StyledOperationSvg = styled(SvgWrapper)`
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
`;
