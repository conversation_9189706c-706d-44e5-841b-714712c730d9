import styled, { css } from 'styled-components';
import { SynthesisType } from './hooks/useSynthesis';
import ModalContent from '@/commons/ModalContent';
import SvgWrapper from '@/components/SvgWrapper';

export const PetBedManufactureTitleContainer = styled.div`
  width: 20rem;
  height: 5.3125rem;
  flex-shrink: 0;
  background-image: url('/image/petDesc_title.webp');
  background-size: 20rem 5.3125rem;
  background-repeat: no-repeat;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  pointer-events: auto;
  & + div {
    pointer-events: auto;
  }
`;

export const PetBedManufactureTitle = styled.p`
  margin: 0;
  margin-bottom: 0.5rem;
  color: #fff;
  text-align: center;
  font-family: 'Baloo 2';
  font-size: 2.5rem;
  font-style: normal;
  font-weight: 800;
  line-height: 100%;
  text-transform: capitalize;
  position: relative;
  z-index: 1;
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.25rem #664830;
    text-stroke: 0.25rem #664830;
    z-index: -1;
    left: 0;
  }
`;

export const StyledModalContent = styled(ModalContent)<{ $componentType?: SynthesisType }>`
  ${({ $componentType = 'tool' }) =>
    $componentType === 'petBed'
      ? css`
          border: 0.5rem solid #efbd73;
          box-shadow: none;
        `
      : css`
          border: 0.25rem solid #140f08;
          box-shadow: inset 0 0 0 0.5rem #ed9800;
        `}
`;

export const StyledHelpIcon = styled(SvgWrapper)`
  display: flex;
  width: 2rem;
  height: 2rem;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 0.125rem solid #ff8316;
  background: #fff;
  box-shadow: 0 0.142875rem 0 0 rgba(0, 0, 0, 0.25);
  cursor: pointer;
  position: absolute;
  left: 1.5625rem;
  top: 1.0625rem;
  z-index: 2;
  & > svg {
    width: 0.875rem;
    height: 1.375rem;
  }
`;
