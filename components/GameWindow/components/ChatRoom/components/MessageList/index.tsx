import { useChatRoomContextDispatch, useChatRoomContextSelector } from '../../context';
import styled from 'styled-components';
import {
  useMemo,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useLayoutEffect,
  useState,
} from 'react';
import { ChatData } from '@/game/TS/Chat/ChatData';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import { MessageItem, MessageSystemItem } from './MessageItem';
import { RowComponentProps } from 'react-window';
import { MessageListContainer } from './styles';
import DynamicRow, { useDynamicItemSize } from './DynamicRow';
import { throttle } from 'lodash';

dayjs.extend(isToday);

type ComponentType = 'preview' | 'primary';

const StyledTimestamp = styled.p`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0rem auto;
`;

export interface IMessageListRef {
  scrollToBottom: () => void;
  forceUpdate: () => void;
}

interface IMessageListProps {
  componentType?: ComponentType;
  handleReplyFocus?: () => void;
}

const MessageList = forwardRef<IMessageListRef, IMessageListProps>(
  ({ componentType = 'primary', handleReplyFocus = () => false }, ref) => {
    const messages = useChatRoomContextSelector((state) => state.messages);
    const selectedTab = useChatRoomContextSelector((state) => state.selectedTab);
    const adminOnly = useChatRoomContextSelector((state) => state.adminOnly);
    const isLockedScroll = useChatRoomContextSelector((state) => state.isLockedScroll);
    const contextDispatch = useChatRoomContextDispatch();
    const lastMessageCountRef = useRef(0);
    const lastSelectedTab = useRef(0);
    const isScrollToBottomRef = useRef(false);
    const [, forceUpdate] = useState({});

    const filterMessages = useMemo(() => {
      const notEmptyMessage = messages.filter((item) => item.content && item.content.trim());

      if (adminOnly && componentType === 'primary') {
        const arr = notEmptyMessage.filter((item) => item.isTime || item.admin);
        const reduceArr = arr.reduce((newArr, curItem) => {
          if (newArr.length === 0) {
            newArr.push(curItem);
            return newArr;
          }
          if (curItem.isTime && newArr[newArr.length - 1].isTime) {
            newArr[newArr.length - 1] = curItem;
          } else {
            newArr.push(curItem);
          }
          return newArr;
        }, [] as ChatData[]);
        if (reduceArr.length === 1 && reduceArr[0].isTime) {
          return [];
        }

        const lastItem = reduceArr[reduceArr.length - 1];
        if (lastItem && lastItem.isTime) {
          reduceArr.pop();
        }

        return reduceArr;
      } else {
        // 如果最后一条是系统时间的信息则直接过滤掉
        const lastItem = notEmptyMessage[notEmptyMessage.length - 1];
        if (lastItem && lastItem.isTime) {
          notEmptyMessage.pop();
        }
        return notEmptyMessage;
      }
    }, [messages, adminOnly, componentType, selectedTab]);

    const { listRef, setSize, getSize } = useDynamicItemSize({
      itemCount: filterMessages.length,
    });

    const latestFilterMessagesRef = useRef<ChatData[]>(filterMessages);
    latestFilterMessagesRef.current = filterMessages;

    const scrollToBottom = () => {
      isScrollToBottomRef.current = true;
      if (componentType === 'primary') {
        contextDispatch({
          type: 'UPDATE',
          payload: { isLockedScroll: false, showNewMessageButton: false },
        });
      }
      if (listRef.current?.scrollToRow && latestFilterMessagesRef.current?.length) {
        setTimeout(() => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              if (latestFilterMessagesRef.current.length) {
                listRef.current?.scrollToRow({
                  index: Math.max(latestFilterMessagesRef.current.length - 1, 0),
                  align: 'end',
                  behavior: 'instant',
                });
                forceUpdate({});
              }
            });
          });
        }, 600);
      }
    };

    const latestScrollToBottom = useRef<() => void>(scrollToBottom);
    latestScrollToBottom.current = scrollToBottom;

    useImperativeHandle(ref, () => ({
      scrollToBottom: () => {
        latestScrollToBottom.current?.();
      },
      forceUpdate: () => {
        forceUpdate({});
      },
    }));

    useEffect(() => {
      if (!isLockedScroll) {
        scrollToBottom();
      }
      if (componentType === 'preview') {
        scrollToBottom();
      }
    }, [messages, isLockedScroll, componentType]);

    useEffect(() => {
      lastMessageCountRef.current = messages.length;
    }, [messages]);

    useLayoutEffect(() => {
      if (componentType === 'preview') return;
      lastSelectedTab.current = selectedTab;
      // TODO:  临时解决渲染过程拿不到实际高度，导致滚动底部有问题
      setTimeout(() => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            // pref
            forceUpdate({});
          });
        });
      }, 500);
    }, [selectedTab, adminOnly]);

    const throttleForceUpdate = throttle(() => {
      forceUpdate({});
    }, 500);

    useLayoutEffect(() => {
      if (componentType === 'primary' && selectedTab === lastSelectedTab.current) {
        if (messages.length > lastMessageCountRef.current) {
          contextDispatch({ type: 'UPDATE', payload: { showNewMessageButton: true } });
        }
      }
    }, [messages, componentType]);

    const handleScroll = () => {
      throttleForceUpdate();

      if (componentType === 'preview') return;
      if (listRef.current?.element) {
        const el = listRef.current.element;
        const isAtBottom = el.scrollHeight - el.scrollTop <= el.clientHeight + 1;
        if (isAtBottom) {
          contextDispatch({
            type: 'UPDATE',
            payload: { isLockedScroll: false, showNewMessageButton: false },
          });
        } else {
          contextDispatch({ type: 'UPDATE', payload: { isLockedScroll: true } });
        }
      }
    };

    const isPrimaryType = componentType === 'primary';

    return (
      <MessageListContainer
        listRef={listRef}
        rowProps={{ items: filterMessages }}
        rowCount={filterMessages.length}
        rowHeight={(index: number) => {
          return getSize(selectedTab, index);
        }}
        onResize={() => {
          forceUpdate({});
        }}
        rowComponent={(props) => {
          return (
            <RowComponent
              {...props}
              setSize={setSize}
              selectedTab={selectedTab}
              isPrimaryType={isPrimaryType}
              handleReplyFocus={handleReplyFocus}
            />
          );
        }}
        onScroll={() => {
          handleScroll();
        }}
        $isPrimary={isPrimaryType}></MessageListContainer>
    );
  }
);

const RowComponent = ({
  index,
  style,
  items,
  setSize,
  selectedTab,
  isPrimaryType,
  handleReplyFocus,
}: RowComponentProps<any> & {
  setSize: (selectedTab: number, index: number, size: number) => void;
  selectedTab: number;
  isPrimaryType: boolean;
  handleReplyFocus?: () => void;
}) => {
  return (
    <div style={style}>
      <DynamicRow
        data={items}
        index={index}
        setSize={setSize}
        selectedTab={selectedTab}
        renderItem={(message: ChatData) => {
          // // mock data
          // if (message.playerId === "tb1pp2nlgc8907a7jsnsfeeaa3ggd8t74ntksj93xmdued7q8ww7wx4qamruzp") {
          //   message.admin = true;
          // }

          if (message.isTime) {
            const timestamp = message.timestamp;
            const isTodayTime = dayjs(timestamp).isToday();

            const format = isTodayTime ? 'HH:mm' : 'MM/DD/YYYY HH:mm';

            return (
              <StyledTimestamp key={message.uuid}>
                {dayjs(timestamp).format(format)}
              </StyledTimestamp>
            );
          }

          if (message.isSystem) {
            return (
              <MessageSystemItem key={message.uuid} message={message} isPrimary={isPrimaryType} />
            );
          }

          return (
            <MessageItem
              key={message.uuid}
              message={message}
              isPrimary={isPrimaryType}
              handleReplyFocus={handleReplyFocus}
            />
          );
        }}
      />
    </div>
  );
};

MessageList.displayName = 'MessageList';
export default MessageList;
