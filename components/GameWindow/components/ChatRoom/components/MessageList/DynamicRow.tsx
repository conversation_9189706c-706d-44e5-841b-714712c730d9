import { useAppSelector } from '@/hooks/useStore';
import { useCallback, useLayoutEffect, useRef, useState } from 'react';
import { ListImperativeAPI } from 'react-window';
import styled from 'styled-components';

interface DynamicRowProps<T> {
  data: T[];
  index: number;
  setSize: (selectedTab: number, index: number, size: number) => void;
  renderItem: (item: T) => React.ReactNode;
  selectedTab: number;
}

interface UseDynamicItemSizeProps {
  itemCount: number;
}

interface UseDynamicItemSizeReturn {
  listRef: React.RefObject<ListImperativeAPI>;
  setSize: (selectedTab: number, index: number, size: number) => void;
  getSize: (selectedTab: number, index: number) => number;
}

export function useDynamicItemSize({
  itemCount,
}: UseDynamicItemSizeProps): UseDynamicItemSizeReturn {
  const listRef = useRef<ListImperativeAPI>(null);
  const sizeMap = useRef<{
    [key: number]: {
      [k: number]: number;
    };
  }>({});
  const setSize = useCallback((selectedTab: number, index: number, size: number) => {
    sizeMap.current = {
      ...sizeMap.current,
      [selectedTab]: { ...sizeMap.current[selectedTab], [index]: size },
    };
  }, []);

  const getSize = (selectedTab: number, index: number) => {
    return sizeMap.current[selectedTab]?.[index] || 50;
  };

  return {
    listRef,
    setSize,
    getSize,
  };
}

const DynamicRow = <T,>({ data, selectedTab, setSize, index, renderItem }: DynamicRowProps<T>) => {
  const rowRef = useRef<HTMLDivElement>(null);
  const [, setDimensions] = useState({ width: 0, height: 0 });
  const isPortrait = useAppSelector((state) => state.AppReducer.isPortrait);
  useLayoutEffect(() => {
    if (rowRef.current) {
      const rect = rowRef.current.getBoundingClientRect();
      const height = isPortrait ? rect.width : rect.height;
      // HACK写法， 组件挂载后重新渲染一次
      setDimensions({ width: rect.width, height: rect.height });
      setSize(selectedTab, index, height);
    }
  }, [setSize, selectedTab, index, isPortrait]);

  const item = data[index];
  return <StyledItemWrapper ref={rowRef}>{item ? renderItem(item) : null}</StyledItemWrapper>;
};

const StyledItemWrapper = styled.div`
  position: relative;
  width: calc(100% - 0.5rem);
  display: flex;
  align-content: center;
  justify-content: center;
  padding-top: 0.5rem;
`;

export default DynamicRow;
