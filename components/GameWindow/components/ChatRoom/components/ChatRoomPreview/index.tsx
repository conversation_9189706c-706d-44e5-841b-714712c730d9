import { SpriteSvg } from '@/components/SvgWrapper';
import MessageList, { IMessageListRef } from '../MessageList';
import { PreviewButton, PreviewButtonSvg, PreviewMessageBox, StyleButtonBox } from './styles';
import { IS_MOBILE_ENV } from '@/constant';
import { useChatRoomContextSelector } from '../../context';
import { useLayoutEffect, useRef } from 'react';
import OrderQuery from './OrderQuery';

interface IChatRoomPreviewProps {
  onExpand: () => void;
}

const ChatRoomPreview: React.FC<IChatRoomPreviewProps> = ({ onExpand }) => {
  const haveNewMessage = useChatRoomContextSelector((state) => state.haveNewMessage);
  const scrollRef = useRef<IMessageListRef>(null);
  const typeList = useChatRoomContextSelector((state) => state.typeList);

  useLayoutEffect(() => {
    if (scrollRef.current) {
      setTimeout(() => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            scrollRef.current?.forceUpdate?.();
            scrollRef.current?.scrollToBottom?.();
          });
        });
      }, 1000);
    }
  }, [typeList]);

  return (
    <>
      <StyleButtonBox>
        <OrderQuery />
        <PreviewButton onClick={onExpand}>
          <PreviewButtonSvg $withNew={haveNewMessage}>
            <SpriteSvg id="message" />
          </PreviewButtonSvg>
          <span className="text">Enter</span>
        </PreviewButton>
      </StyleButtonBox>
      {!IS_MOBILE_ENV && (
        <PreviewMessageBox onClick={onExpand}>
          <MessageList ref={scrollRef} componentType="preview" />
        </PreviewMessageBox>
      )}
    </>
  );
};

export default ChatRoomPreview;
