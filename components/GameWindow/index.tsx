import { GameWindowView } from './style';
import React, { useEffect, useMemo, useRef, useState } from 'react';

// import App from '../../src/game/App'
import AvatarPage from '../../AvatarOrdinalsBrowser/components/AvatarPage';
import {
  IAppState,
  IBasicSummaryData,
  IGameState,
  IPotatoTime,
  IWallNft,
  SCENE_TYPE,
  STORAGE_MENU_ENUM,
  TOKEN_TYPE_ENUM,
} from '@/constant/type';
import {
  FATHER_TYPE_ENUM,
  IAvatarMetadata,
  ICollection,
  UsableAddressDataType,
} from '@/AvatarOrdinalsBrowser/constant/type';
import GameOpWindow from './GameOpWindow';
import AvatarDataNFT from '@/game/TS/Data/AvatarDataNFT';
import AvatarDataFT from '@/game/TS/Data/AvatarDataFT';
import FTWindow from '../FTWindow';
import NFTWindow from '../NFTWindow';
import { IS_TEST_ENV } from '@/AvatarOrdinalsBrowser/constant';
import GlobalSpaceEvent, { GlobalDataKey, SpaceStatus } from '@/game/Global/GlobalSpaceEvent';
import TestApiWindow from '../TestApiWindow';
import { ButlerData, ButlerUtil } from '@/game/Global/GlobalButlerUtil';
import { ConfigManager } from '@/game/Config/ConfigManager';
import { useSelector } from 'react-redux';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import GameApiWindow from '@/components/GameWindow/GameApiWindow';
import { getBeijingMidnightTimestamp, getLocalSession } from '@/utils';
import { useNetWork } from '@/game/TS/useNetWork';
import CommunityApiWindow from '@/components/GameWindow/CommunityApiWindow';
import { GAME_SOCKET_URL } from '@/constant';
import { getDurabilityTips } from '@/utils/durabilityTips';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import TaskMission, { TaskMissionRef } from '../EditAvatarPage/StoreMenu/components/TaskMission';
import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import useIsLogin from '@/hooks/useIsLogin';
import dynamic from 'next/dynamic';
import { TreeConfig } from '@/game/Config/TreeConfig';
import AvatarObject from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject';
import NewChatRoom from './components/ChatRoom';
import PetPanel from './components/PetPanel';
import PetSubmitChainModal from '../EditAvatarPage/SubmitChain/SubmitModal/PetSubmitChainModal';
import { BagQuickBar } from '../EditAvatarPage/BagModal';
import { BagQuickBarViewWrapper } from '../EditAvatarPage/BagModal/style';
import { AnimatePresence } from 'framer-motion';
import { useAppDispatch } from '@/hooks/useStore';
import { resetModalState } from '@/store/modal';
import { AvatarUtil } from '@/AvatarOrdinalsBrowser/renderAvatar/AvatarUtil';

const App = dynamic(() => import('../../game/App'), { ssr: false });

interface IProps {
  collectionData: ICollection | null;
  onClaimPotato?: () => void;
  potatoTime?: IPotatoTime | null;
  submitChainSlot?: Function;
  deleteCollectionSlot?: Function;
  isFrontend: boolean;
  defaultInscriptionId?: string;
  defaultAvatarMetadata?: IAvatarMetadata;
  basicSummaryData: IBasicSummaryData | null;
  showWallNftList: IWallNft[];
  doorKey?: {
    hasAresKey: boolean;
    aresKeyUnlocksTime: number;
    openCallback: Function;
  };
  isDisabledPet?: boolean; // 是否禁用宠物（这个字段是临时的，在升级铭文那一版本需要改成seq判断）
  screenTypeCallback?: Function; //返回场景类型
  butlerData: ButlerData | null;
  isVisitor: boolean;
  visitorAvatarMetadata?: IAvatarMetadata;
  hiddenChangeScreen?: boolean;
  usableAddressData?: UsableAddressDataType;
}

export default function GameWindow({
  onClaimPotato,
  potatoTime,
  collectionData,
  submitChainSlot,
  deleteCollectionSlot,
  isFrontend,
  defaultInscriptionId,
  defaultAvatarMetadata,
  basicSummaryData,
  showWallNftList,
  doorKey,
  isDisabledPet,
  screenTypeCallback,
  butlerData,
  isVisitor,
  visitorAvatarMetadata,
  hiddenChangeScreen,
  usableAddressData,
}: IProps) {
  const {
    btcAddress,
    userBasicInfo,
    dogEasterEgg,
    easterEggInfo,
    menuVisible,
    sceneType,
    whackAMoleEasterEgg,
    storageMenu,
    isMobile,
  } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const { bagInventoryList } = useSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer
  );
  const dispatch = useAppDispatch();

  const isLogin = useIsLogin();
  const [curShortcut, setCurShortcut] = useState('1');
  const pizzaActivity = getPizzaActivity();
  const myPlayer = GetMyPlayer();
  const { connect, disConnect } = useNetWork();
  const shortcutChangedByKeyPress = useRef(false);
  const taskMissionRef = useRef<TaskMissionRef>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [shortcutClickTime, setShortcutClickTime] = useState(0);
  const isPlaying = [STORAGE_MENU_ENUM.PLAY_MENU, null].includes(storageMenu) && !!btcAddress;

  useEffect(() => {
    if (shortcutClickTime) {
      if (timerRef.current) clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        setShortcutClickTime(0);
      }, 1500);
    }

    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [shortcutClickTime]);

  useEffect(() => {
    myPlayer.axeParams = {
      tag: 0,
      currentDurability: 0,
      userItemId: '',
    };
    let haveNew = false;
    bagInventoryList.forEach((item) => {
      const shortcut = item.shortcut || '';
      if (item.isNew && shortcut.length > 0 && myPlayer.isNewServerItemId(item.userItemId)) {
        if (curShortcut == shortcut) return;
        haveNew = true;
        setTimeout(() => {
          setCurShortcut(shortcut);
        }, 1);
      }
    });
    if (haveNew) {
      return;
    }
    bagInventoryList.forEach((item, index) => {
      if (!item.isNew) {
        myPlayer.saveServerItemId(item.userItemId);
      }
      if (item.shortcut === curShortcut) {
        myPlayer.axeParams = {
          tag: Number(item.tag),
          userItemId: item.userItemId,
          currentDurability: item.currentDurability,
        };
        // 添加耐久度提示
        if (
          myPlayer.axeParams.currentDurability !== undefined &&
          shortcutChangedByKeyPress.current
        ) {
          getDurabilityTips(myPlayer.axeParams.currentDurability);
        }
      }
    });
    // 重置快捷键切换标志
    shortcutChangedByKeyPress.current = false;
  }, [bagInventoryList, curShortcut]);

  // 监听按下快捷栏按键
  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(
      ['1', '2', '3', '4', '5'],
      (event: KeyboardEvent) => {
        shortcutChangedByKeyPress.current = true;
        const now = Date.now();
        setShortcutClickTime(now);
        setCurShortcut(event.key);
      }
    );
    return () => {
      cancel();
    };
  }, []);

  const handleClickItem = (key: string) => {
    shortcutChangedByKeyPress.current = true;
    const now = Date.now();
    setShortcutClickTime(now);
    setCurShortcut(key);
  };

  useEffect(() => {
    myPlayer.dogEasterEgg = dogEasterEgg;
  }, [dogEasterEgg]);

  useEffect(() => {
    myPlayer.whackAMoleEasterEgg = whackAMoleEasterEgg;
  }, [whackAMoleEasterEgg]);

  useEffect(() => {
    myPlayer.showEasterEggButton = false;
    if (easterEggInfo) {
      // const domain = '3.uniworlds.io'
      const domain = window.location.hostname;
      const domainList = domain.split('.');
      let finishCount = 0;
      easterEggInfo.forEach((item) => {
        if (item.isSuccess) {
          finishCount++;
        }
        if (item.domainName.split('.')[0] === domainList[0] && !item.isSuccess) {
          myPlayer.showEasterEggButton = true;
        }
      });
      if (finishCount >= 4) {
        myPlayer.showEasterEggButton = false;
      }
    }
  }, [easterEggInfo]);

  useEffect(() => {
    myPlayer.userBasicInfo = userBasicInfo;
    if (myPlayer.refreshTimeStamp === 0) {
      myPlayer.refreshTimeStamp = getBeijingMidnightTimestamp() + 24 * 60 * 60 * 1000;
    }
  }, [userBasicInfo]);

  useEffect(() => {
    // 账号切换清空彩蛋树
    TreeConfig.getInstance().clearEasterEggTree();
    dispatch(resetModalState());
  }, [btcAddress]);

  useEffect(() => {
    myPlayer.btcAddress = btcAddress;
    const socket_url = GAME_SOCKET_URL || 'wss://socket-test.satworld.io';
    if (btcAddress.length > 0) {
      const session = getLocalSession(btcAddress);
      myPlayer.sessionId = session.sessionId;
      pizzaActivity.refreshUpdateActivityData();
      connect(socket_url);
    }
    return () => {
      disConnect();
    };
  }, [connect, disConnect, btcAddress, myPlayer, pizzaActivity]);

  const exportPreviewImage = (): string => {
    const renderTarget: HTMLElement | null = document.getElementById('render-target');
    if (renderTarget === null) {
      console.error('render-target not found');
      return '';
    }

    const canvas = renderTarget.getElementsByTagName('canvas')[0];
    if (canvas === null) {
      console.error('canvas not found');
      return '';
    }
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    // 计算正方形的边长（取较小的边长）
    const squareSize = Math.min(canvasWidth, canvasHeight);
    // 计算正方形的起始位置，使其位于canvas的中心
    const offsetX = (canvasWidth - squareSize) / 2;
    const offsetY = (canvasHeight - squareSize) / 2;

    // 创建一个新的canvas用于导出
    const exportCanvas = document.createElement('canvas');
    exportCanvas.width = squareSize;
    exportCanvas.height = squareSize;
    // 获取导出canvas的上下文
    const exportCtx = exportCanvas.getContext('2d');
    // 将原canvas中间的正方形区域绘制到新canvas
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    exportCtx.drawImage(
      canvas,
      offsetX,
      offsetY,
      squareSize,
      squareSize,
      0,
      0,
      squareSize,
      squareSize
    );
    // 导出为图片URL
    const imageURL = exportCanvas.toDataURL('image/png');
    return imageURL;
  };

  // 渲染资产
  const renderBasicSummaryData = () => {
    if (basicSummaryData) {
      const { brc20Result, runesResult, catResult } = basicSummaryData;
      const nftData: AvatarDataNFT[] = [];
      for (let i = 0; i < showWallNftList.length; i++) {
        nftData.push(new AvatarDataNFT(showWallNftList[i].position, showWallNftList[i].content));
      }
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.NftData, nftData);
      const brc20_runes_cat20: AvatarDataFT[] = [];
      for (let i = 0; i < brc20Result.length; i++) {
        brc20_runes_cat20.push(new AvatarDataFT(TOKEN_TYPE_ENUM.Brc20, brc20Result[i]));
      }
      for (let i = 0; i < runesResult.length; i++) {
        brc20_runes_cat20.push(new AvatarDataFT(TOKEN_TYPE_ENUM.Runes, runesResult[i]));
      }
      if (catResult) {
        for (let i = 0; i < catResult.length; i++) {
          brc20_runes_cat20.push(new AvatarDataFT(TOKEN_TYPE_ENUM.Cat20, catResult[i]));
        }
      }
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.FtData, brc20_runes_cat20);
    }
  };

  useEffect(() => {
    if (basicSummaryData) {
      const havePotato = basicSummaryData.petInfo.claim;
      const havePlantPotato = (potatoTime && potatoTime.plantTime !== null) || false;

      const showIslandPotato = btcAddress.length > 0 && havePlantPotato && !havePotato;
      GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.ShowIslandPotato, showIslandPotato);
    } else {
      GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.ShowIslandPotato, false);
    }
  }, [basicSummaryData]);

  useEffect(() => {
    renderBasicSummaryData();
  }, [basicSummaryData, showWallNftList]);

  const avatarPageRef: any = useRef(null);
  const updatePath = (key: string, value: string) => {
    avatarPageRef.current.updatePath(key, value);
  };

  useEffect(() => {
    ConfigManager.getInstance().getData(() => undefined);
    ChatManager.getInstance().clientStart();
    GlobalSpaceEvent.SetDataValue<SCENE_TYPE>(GlobalDataKey.SceneType, SCENE_TYPE.Room);
    GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Avatar);

    GlobalSpaceEvent.SetDataValue<string>(GlobalDataKey.UsePetInscriptionId, '');

    const renderTarget: HTMLElement | null = document.getElementById('render-target-game');
    if (renderTarget === null) {
      console.error('render-target not found');
      return;
    }
    ButlerUtil.setVisitor(isVisitor);
    // root.render(
    //   // <React.StrictMode>
    //   <App
    //     debug={IS_AVATAR_PAGE}
    //     onClaimPotato={onClaimPotato}
    //     btcAddress={String(Math.floor(Math.random() * 1000000))}
    //     butlerData={butlerData}\

    //     isVisitor={isVisitor}
    //   />
    //   // </React.StrictMode>,
    // );
    return () => {
      myPlayer.position.set(0, 0, 0);
      myPlayer.refreshTimeStamp = 0;
      ButlerUtil.clearButlerData();
      ChatManager.getInstance().clientStop();
    };
  }, []);

  const renderEl = useMemo(() => {
    return (
      <div id="render-target-game">
        <App onClaimPotato={onClaimPotato} butlerData={butlerData} isVisitor={isVisitor} />
      </div>
    );
  }, []);

  useEffect(() => {
    if (butlerData) {
      ButlerUtil.resetData(butlerData);
      ButlerUtil.enterVisitor();
    }
  }, [butlerData]);
  useEffect(() => {
    GlobalSpaceEvent.SetDataValue<IAvatarMetadata>(
      GlobalDataKey.MyAvatarData,
      isVisitor
        ? AvatarUtil.getMetaData(visitorAvatarMetadata)
        : AvatarUtil.getMetaData(defaultAvatarMetadata)
    );
    GlobalSpaceEvent.SetDataValue<IAvatarMetadata>(
      GlobalDataKey.ButlerAvatarData,
      AvatarUtil.getMetaData(defaultAvatarMetadata)
    );
  }, [defaultAvatarMetadata, isVisitor, visitorAvatarMetadata]);

  return (
    <GameWindowView>
      <AvatarPage
        collectionData={collectionData}
        defaultInscriptionId={defaultInscriptionId}
        defaultAvatarMetadata={defaultAvatarMetadata}
        usableAddressData={usableAddressData}
        submitChainSlot={submitChainSlot}
        hiddenMenu={!menuVisible}
        exportPreviewImage={exportPreviewImage}
        deleteCollectionSlot={deleteCollectionSlot}
        isFrontend={isFrontend}
        avatarObjectLoaded={(avatarObject) => {
          GlobalSpaceEvent.SetDataValue<AvatarObject>(GlobalDataKey.AvatarObject, avatarObject);
        }}
        ref={avatarPageRef}
        lazyLoadGroups={[FATHER_TYPE_ENUM.Action]}
        isDisabledPet={isDisabledPet}
        isLogin={isLogin}
      />
      {/* <div id="render-target-game" />
      <div/> */}
      {renderEl}
      <FTWindow />
      <NFTWindow updatePath={updatePath} isVisitor={isVisitor} />
      <GameOpWindow />
      {/* {!hiddenChangeScreen && <ChangeSceneWindow screenTypeCallback={screenTypeCallback} />} */}
      {IS_TEST_ENV && <TestApiWindow />}
      <NewChatRoom />
      <PetPanel />
      <AnimatePresence>
        {(shortcutClickTime > 0 || isMobile) && isPlaying && (
          <BagQuickBarViewWrapper
            key="bagQuickBarAnimate"
            initial={{
              y: '10rem',
            }}
            animate={{
              y: '-2rem',
            }}
            exit={{
              y: '10rem',
              transition: {
                duration: 0.1,
                ease: 'easeIn',
              },
            }}
            transition={{
              duration: 0.2,
              ease: 'easeIn',
            }}>
            <BagQuickBar
              isDragging={false}
              onlyView
              activeShortcut={curShortcut}
              changeCurShortcut={handleClickItem}
            />
          </BagQuickBarViewWrapper>
        )}
      </AnimatePresence>

      <PetSubmitChainModal />
      {/* 任务简报 */}
      {btcAddress && sceneType !== 1 && <TaskMission ref={taskMissionRef} />}
      <GameApiWindow />
      <CommunityApiWindow />
      {/*<GameChatWindow/>*/}
      {/* <Stamina /> */}
    </GameWindowView>
  );
}
