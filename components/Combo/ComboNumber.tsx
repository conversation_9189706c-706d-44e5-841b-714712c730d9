import { px2rem } from '@/utils/px2rem';
import { memo } from 'react';
import styled from 'styled-components';

/**
 * 新需求：combo连击数字颜色变化
 * [1-5]: #ffffff
 * [6-15]: #fdd880
 * [number > 15]: #fd9b6b
 */

// 根据连击数获取颜色
const getComboColor = (number: number): string => {
  if (number <= 5) {
    return '#ffffff'; // 白色
  } else if (number <= 15) {
    return '#fdd880'; // 淡黄色
  } else {
    return '#fd9b6b'; // 橙色
  }
};

const getComboNumberSize = (number: number): number => {
  if (number <= 5) {
    return 64;
  } else if (number <= 15) {
    return 66;
  } else {
    return 68;
  }
};

const ComboNumberWrapper = styled.div<{
  numberColor: string;
  numberSize: number;
}>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-6deg);
  font-family: 'Shrikhand', cursive;
  font-size: ${({ numberSize }) => px2rem(numberSize)}rem;
  /* color: #ffbc23; // 金黄色 */

  text-shadow:
    -0.25rem 0.1875rem 0 #000,
    0.1875rem 0.125rem 0rem #000,
    0.1875rem -0.125rem 0 #000,
    0.1875rem 0.1875rem 0 #000;
  -webkit-text-stroke: 0.125rem black; // 另一种描边方式
  filter: drop-shadow(0 0 0.3125rem rgba(255, 100, 0, 0.7)); // 橙色光晕效果
  display: flex;
  align-items: center;
  .combo-number {
    color: ${({ numberColor }) => numberColor};
  }
`;

const HitsText = styled.span`
  font-size: 2.5rem; // 较小的字体大小
  color: #f6bf93;
`;

interface ComboNumberProps {
  number: number;
}

const ComboNumber = memo(({ number }: ComboNumberProps) => {
  const numberColor = getComboColor(number);
  return (
    <ComboNumberWrapper numberColor={numberColor} numberSize={getComboNumberSize(number)}>
      <span className="combo-number">{number}</span> <HitsText>Hits</HitsText>
    </ComboNumberWrapper>
  );
});

export default ComboNumber;
