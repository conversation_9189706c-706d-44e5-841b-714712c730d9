import styled from 'styled-components';
import { useRef, useState } from 'react';
import { useAppSelector } from '@/hooks/useStore';

interface SliderProps {
  min: number;
  max: number;
  value: number;
  onChange: (value: number) => void;
}

const SliderWrapper = styled.div`
  width: 100%;
  padding: 1rem 0 0 0;
  display: flex;
  flex-direction: column;
  user-select: none;
`;

const SliderTrack = styled.div`
  position: relative;
  width: 100%;
  height: 0.5rem;
  background: #d6c7ad;
  border-radius: 0.25rem;
`;

const SliderFilled = styled.div<{ percent: number }>`
  position: absolute;
  left: 0;
  top: 0;
  height: 0.5rem;
  border-radius: 0.25rem;
  background: #fc7922;
  width: ${({ percent }) => percent}%;
  transition: background 0.2s;
`;

const SliderThumb = styled.div<{ percent: number; dragging: boolean }>`
  position: absolute;
  top: 50%;
  left: ${({ percent }) => percent}%;
  transform: translate(-50%, -50%);
  width: 1rem;
  height: 1rem;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 0.125rem 0.3125rem black;
  cursor: pointer;
  transition:
    box-shadow 0.2s,
    border 0.2s;
  z-index: 2;
  ${({ dragging }) =>
    dragging
      ? 'box-shadow: 0 0.25rem 1rem rgba(252,121,34,0.25); border: 0.1875rem solid #fc7922;'
      : ''}
`;

const SliderLabels = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: #a58061;
  font-family: inherit;
`;

const Slider: React.FC<SliderProps> = ({ min, max, value, onChange }) => {
  const [dragging, setDragging] = useState(false);
  const trackRef = useRef<HTMLDivElement>(null);
  const isPortrait = useAppSelector((state) => state.AppReducer.isPortrait);

  // 计算百分比
  const percent = ((value - min) / (max - min)) * 100;

  // 拖动处理
  const handleMove = (clientX: number) => {
    if (!trackRef.current) return;
    const rect = trackRef.current.getBoundingClientRect();
    let x = clientX - rect.left;
    const limit = isPortrait ? rect.height : rect.width;
    if (isPortrait) {
      x = clientX - rect.top;
    }
    x = Math.max(0, Math.min(x, limit));

    const newValue = Math.round((x / limit) * (max - min) + min);
    onChange(newValue);
  };

  const onMouseDown = (e: React.MouseEvent) => {
    setDragging(true);
    if (isPortrait) {
      handleMove(e.clientY);
    } else {
      handleMove(e.clientX);
    }

    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('mouseup', onMouseUp);
  };
  const onMouseMove = (e: MouseEvent) => {
    if (isPortrait) {
      handleMove(e.clientY);
    } else {
      handleMove(e.clientX);
    }
  };
  const onMouseUp = () => {
    setDragging(false);
    window.removeEventListener('mousemove', onMouseMove);
    window.removeEventListener('mouseup', onMouseUp);
  };

  // 移动端支持
  const onTouchStart = (e: React.TouchEvent) => {
    setDragging(true);
    if (isPortrait) {
      handleMove(e.touches[0].clientY);
    } else {
      handleMove(e.touches[0].clientX);
    }
    window.addEventListener('touchmove', onTouchMove);
    window.addEventListener('touchend', onTouchEnd);
  };
  const onTouchMove = (e: TouchEvent) => {
    if (isPortrait) {
      handleMove(e.touches[0].clientY);
    } else {
      handleMove(e.touches[0].clientX);
    }
  };
  const onTouchEnd = () => {
    setDragging(false);
    window.removeEventListener('touchmove', onTouchMove);
    window.removeEventListener('touchend', onTouchEnd);
  };

  return (
    <SliderWrapper>
      <SliderTrack ref={trackRef} onMouseDown={onMouseDown} onTouchStart={onTouchStart}>
        <SliderFilled percent={percent} />
        <SliderThumb
          percent={percent}
          dragging={dragging}
          style={{ touchAction: 'none' }}
          onMouseDown={onMouseDown}
          onTouchStart={onTouchStart}
        />
      </SliderTrack>
      <SliderLabels>
        <span>{min}</span>
        <span>{max}</span>
      </SliderLabels>
    </SliderWrapper>
  );
};

export default Slider;
