import AvatarPartsModel from './Avatar/AvatarPartsModel';
import { IAvatarMetadata, IFatherInscription, IInitDefaultOptions } from '../constant/type';
import AvatarMain from './AvatarMain';

/**
 * Public API for BeatSaberAvatar.js.
 *
 * @package BeatSaberAvatar.js
 * @type {Window.BeatSaberAvatar}
 * @link https://github.com/roydejong/BeatSaberAvatar.js
 */
export const BeatSaberAvatar = class {
  static setup(
    domElement: HTMLElement | null,
    globalParts: IFatherInscription[],
    avatarData: IAvatarMetadata,
    options: IInitDefaultOptions
  ) {
    // Init avatar part library
    AvatarPartsModel.init(globalParts);

    // Create and return renderer instance
    let renderInstance = new AvatarMain();
    renderInstance.init(options, domElement);
    renderInstance.setAvatarData(avatarData);
    return {
      renderInstance,
      avatarPartsModel: AvatarPartsModel,
    };
  }
};
