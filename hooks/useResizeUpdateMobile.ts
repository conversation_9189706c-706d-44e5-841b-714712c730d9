import { debounce } from 'lodash';
import { useEffect } from 'react';
import { useAppDispatch } from './useStore';
import isMobile from 'ismobilejs';
import { setIsMobile, setIsPortrait } from '@/store/app';

function useResizeUpdateMobile() {
  const dispatch = useAppDispatch();
  useEffect(() => {
    const isPortrait = window.matchMedia('(orientation: portrait)').matches;
    dispatch(setIsPortrait(isPortrait));

    const update = () => {
      const mobileInfo = isMobile();
      const isPortrait = window.matchMedia('(orientation: portrait)').matches;
      dispatch(setIsMobile(mobileInfo.any));
      dispatch(setIsPortrait(isPortrait));
    };

    const handleResize = debounce(() => {
      update();
    }, 500);

    const resizeObserver = new ResizeObserver(handleResize);
    if (document.documentElement) {
      resizeObserver.observe(document.documentElement);
    }
    return () => {
      resizeObserver.disconnect();
      handleResize.cancel();
    };
  }, []);
}

export default useResizeUpdateMobile;
