import { TooltipRef } from 'rc-tooltip';
import { RefObject, useState } from 'react';
import { css } from '@emotion/css';

function useToolTipPortraitFix(triggerRef: RefObject<TooltipRef>, parentNode = document.body) {
  const [classStyle, setClassStyle] = useState('');

  const handleVisibleChange = (visible: boolean) => {
    const adjustAlign = (nativeEl: HTMLElement, parent: HTMLElement) => {
      const parentRect = parent.getBoundingClientRect();
      const elRect = nativeEl.getBoundingClientRect();
      const top = parentRect.right - elRect.right + elRect.height / 2;
      const left = elRect.top - parentRect.top + elRect.width / 2;

      const style = css`
        inset: auto !important;
        top: ${top}px !important;
        left: ${left}px !important;
      `;
      setClassStyle(style);

      // parent.setAttribute(
      //   'style',
      //   `--inset:auto !important;--top: ${top}px !important; --left: ${left}px !important;`
      // );
    };

    if (visible && triggerRef.current && parentNode) {
      const parent = parentNode;
      const nativeEl = triggerRef.current.nativeElement;

      if (nativeEl && parent) {
        adjustAlign(nativeEl, parent);
      } else {
        setTimeout(() => {
          handleVisibleChange(visible);
        }, 0);
      }
    }
  };

  return { classStyle, handleVisibleChange };
}
export default useToolTipPortraitFix;
