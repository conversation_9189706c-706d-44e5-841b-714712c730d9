import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { getLocalSession } from '@/utils';
import { TwitterAuthService } from '@/server';
import { IS_RELEASE_ONLINE, TWITTER_AUTH_MAX_TIMES, TWITTER_AUTH_TIME } from '@/constant';
import toast from 'react-hot-toast';
import { setShowConnectWallet, setUserBasicInfo } from '@/store/app';
import useLatest from './useLatest';

interface UseTwitterAuthProps {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  checkOnMount?: boolean; // 新增参数，控制是否在挂载时检查授权状态
}

/**
 * @deprecated 不再需要推特验证
 */
export const useTwitterAuth = ({
  onSuccess,
  onError,
  checkOnMount = false, // 默认不检查
}: UseTwitterAuthProps = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false); // 新增状态，跟踪是否已授权
  const authWindowRef = useRef<Window | null>(null);
  const pollingIntervalRef = useRef<number | null>(null);
  const sessionIdRef = useRef<string | null>(null);
  const pollCountRef = useRef<number>(0);
  const errorCountRef = useRef<number>(0); // 跟踪连续错误次数
  const dispatch = useDispatch();
  const { btcAddress, userBasicInfo } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  const encodeBase64 = (str: string): string => {
    // 使用内置的btoa函数进行Base64编码
    // 注意：btoa不能直接处理Unicode字符，需要先进行UTF-8编码
    try {
      return btoa(
        encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (_, p1) =>
          String.fromCharCode(parseInt(p1, 16))
        )
      );
    } catch (e) {
      return str; // 出错时返回原始字符串
    }
  };

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    // 重置轮询计数和错误计数
    pollCountRef.current = 0;
    errorCountRef.current = 0;
  }, []);

  // 清理所有资源
  const cleanup = useCallback(() => {
    stopPolling();
    sessionIdRef.current = null;
  }, []);

  // 组件卸载时清理资源
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const checkAuthCap = async (captchaToken: string) => {
    try {
      const sessionId = getLocalSession(btcAddress).sessionId;
      const response = await TwitterAuthService.getAuthUrl({
        captchaToken,
        sessionId,
        address: btcAddress,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to check Twitter auth status:', error);
      throw error;
    }
  };

  // 开始轮询检查授权状态
  const startPolling = useCallback(() => {
    // 确保有会话ID
    if (!sessionIdRef.current) {
      setIsLoading(false);
      setError(new Error('No session ID for authorization'));
      onError && onError(new Error('No session ID for authorization'));
      return;
    }

    // 先停止可能存在的轮询
    // stopPolling();

    // 重置轮询计数
    pollCountRef.current = 0;
    errorCountRef.current = 0;

    // 设置轮询间隔
    pollingIntervalRef.current = window.setInterval(async () => {
      // 增加轮询计数
      pollCountRef.current += 1;

      // 检查是否达到最大轮询次数
      if (pollCountRef.current >= parseInt(TWITTER_AUTH_MAX_TIMES)) {
        cleanup();
        setIsLoading(false);
        const timeoutError = new Error(
          `Authorization timed out after ${TWITTER_AUTH_MAX_TIMES} attempts`
        );
        authWindowRef.current?.close();
        setError(timeoutError);
        onError && onError(timeoutError);
        return;
      }

      try {
        // 调用API检查授权状态
        const response = await TwitterAuthService.checkAuthStatus();

        // 重置错误计数，因为API调用成功
        errorCountRef.current = 0;

        // 处理直接返回布尔值的情况
        const isAuthorized = response.data === true || response.code !== 1;

        // 认证成功后切换UI
        if (response.data === true) {
          const data = IS_RELEASE_ONLINE
            ? {
                ...userBasicInfo,
                twitterFlag: true,
              }
            : userBasicInfo;
          dispatch(setUserBasicInfo(data));
        }

        // 如果已授权（response.data为true）
        if (isAuthorized) {
          authWindowRef.current?.close();
          // 停止轮询和清理资源
          cleanup();

          // 处理成功结果
          setIsLoading(false);
          if (response.code === 1) {
            toast.success('Successfully joined the event!');
          } else {
            toast.error(response.msg);
          }
          onSuccess && onSuccess(true);
          return;
        }

        // 如果response.data为false但窗口仍然打开，继续轮询
      } catch (err) {
        // 增加错误计数
        errorCountRef.current += 1;

        // 只有在达到一定错误次数后才停止轮询
        if (errorCountRef.current > 5) {
          // 允许最多5次连续错误
          authWindowRef.current?.close();
          cleanup();
          setIsLoading(false);
          const networkError = new Error('Authorization failed due to network issues');
          setError(networkError);
          toast.error('Network issues caused Twitter authorization failure');
          onError && onError(networkError);
        }
        // 否则继续轮询
      }
    }, parseInt(TWITTER_AUTH_TIME));
  }, [cleanup, onSuccess, onError, stopPolling]);

  // 新增：检查授权状态的函数
  const checkAuthStatus = useCallback(async () => {
    if (!btcAddress) {
      return false;
    }

    try {
      setIsLoading(true);
      const response = await TwitterAuthService.checkAuthStatus();

      const authorized = response.data === true || response.code !== 1;
      setIsAuthorized(authorized);
      // 轮询到已经授权，则更新redux中的twitterFlag
      if (authorized) {
        const data = IS_RELEASE_ONLINE
          ? {
              ...userBasicInfo,
              twitterFlag: true,
            }
          : userBasicInfo;
        dispatch(setUserBasicInfo(data));
      }

      if (authorized && onSuccess) {
        onSuccess(true);
      }

      return authorized;
    } catch (err) {
      // 只在手动检查时显示错误提示
      toast.error('Check Twitter binding status failed');

      return false;
    } finally {
      setIsLoading(false);
    }
  }, [btcAddress, onSuccess, checkOnMount]);

  // 在组件挂载时检查授权状态
  useEffect(() => {
    if (checkOnMount) {
      checkAuthStatus();
    }

    return cleanup;
  }, [checkOnMount, checkAuthStatus, cleanup, btcAddress]);

  const latestBtcAddress = useLatest(btcAddress);

  // 修改startAuth函数，先检查授权状态
  const startAuth = useCallback(async () => {
    const btcAddress = latestBtcAddress.current;
    if (!btcAddress) {
      toast.error('Connect your wallet');
      dispatch(setShowConnectWallet(true));
      return;
    }

    // 先检查是否已授权
    const alreadyAuthorized = await checkAuthStatus();
    if (alreadyAuthorized) {
      toast.success('Account is already bound, no need to repeat authorization');
      return;
    }

    const { sessionId } = getLocalSession(btcAddress);

    try {
      // 清理之前的状态
      cleanup();

      setIsLoading(true);
      setError(null);

      const encodedSessionId = encodeBase64(sessionId);
      sessionIdRef.current = sessionId;

      // 直接使用授权URL
      const twitterUrl = process.env.GAME_REQUEST_URL;
      const authUrl = `${twitterUrl}/twitter/auth?address=${btcAddress}&sessionId=${encodedSessionId}`;

      // 打开新窗口但不保存引用
      const width = 600;
      const height = 600;
      const left = window.innerWidth / 2 - width / 2;
      const top = window.innerHeight / 2 - height / 2;

      // 直接调用window.open，不赋值给authWindowRef.current
      authWindowRef.current = window.open(
        authUrl,
        'twitter-auth-window',
        `width=${width},height=${height},top=${top},left=${left},toolbar=0,location=0,menubar=0,status=0,scrollbars=1`
      );

      // 开始轮询
      startPolling();
    } catch (err) {
      setIsLoading(false);
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);

      toast.error(`${error.message}`);
      onError && onError(error);
    }
  }, [btcAddress, cleanup, startPolling, checkAuthStatus, onError]);

  // 提供一个手动关闭窗口的方法
  const cancelAuth = useCallback(() => {
    cleanup();
    setIsLoading(false);
    const cancelError = new Error('Authorization was cancelled by user');
    setError(cancelError);
    toast.error('Authorization cancelled by user');
    onError && onError(cancelError);
  }, [cleanup, onError]);

  // 返回当前轮询进度信息
  const getPollingProgress = useCallback(() => {
    return {
      current: pollCountRef.current,
      max: parseInt(TWITTER_AUTH_MAX_TIMES),
      percentage: Math.round((pollCountRef.current / parseInt(TWITTER_AUTH_MAX_TIMES)) * 100),
    };
  }, []);

  return {
    startAuth,
    isLoading,
    error,
    isAuthorized, // 新增返回值，表示当前授权状态
    cancelAuth,
    checkAuthStatus, // 暴露检查函数，允许手动检查
    stopPolling,
    getPollingProgress,
    encodeBase64,
    startPolling,
    checkAuthCap,
  };
};
