export enum UserItemType {
  // 砍树
  AXE = 'Axe',
  // 挖矿
  PICKAXE = 'Pickaxe',
  // 钓鱼
  FISHING_POLE = 'FishingPole',
  // 宠物窝
  PET_SHED = 'PetShed',
  // 宠物祭坛
  PET_ALTAR = 'PetAltar',
}

export enum PetStatus {
  // 休息
  REST = 'Rest',
  // 待机
  IDLE = 'Idle',
  // 跟随
  FOLLOW = 'Follow',
  // 骑乘
  RIDE = 'Ride',
  // 砍树
  AXE = 'Axe',
  // 挖矿
  PICKAXE = 'Pickaxe',
  // 钓鱼
  FISHING_POLE = 'FishingPole',
  // 战斗
  BATTLE = 'Battle',
}
export enum Rarity {
  // 普通
  COMMON = 'common',
  // 罕见
  UNCOMMON = 'uncommon',
  // 稀有
  RARE = 'rare',
  // 史诗
  EPIC = 'epic',
  // 传说
  LEGENDARY = 'legendary',
  // 神话
  MYTHIC = 'mythic',
}

export enum AFFINITY_ENUM {
  FOREST = 'forest',
  WATER = 'water',
  ROCK = 'rock',
}

export enum PetShedRecordStatus {
  // 等待中
  PENDING = 'pending',
  // 生成中
  GENERATING = 'generating',
  // 已生成
  GENERATED = 'generated',
  // 已收走
  COLLECTED = 'collected',
}

export enum PetShedRecordType {
  PET_SHED = 'PetShed',
  PET_ALTAR = 'PetAltar',
}

export enum INVENTORY_TYPE_ENUM {
  equipment = 'equipment', //装备
  resource = 'resource', //耗材
  material = 'material', //材料
  petResource = 'petResource', //宠物资源 宠物窝和祭坛
  /**
   * @description only use for FE
   */
  pet = 'pet', //宠物
}

export enum RewardType {
  potato = 'potato',
  TheLonelyBit = 'TheLonelyBit',
  wangcai = 'wangcai',
  sQUAQ___000 = 'sQUAQ___000',
  sPIZZA___000 = 'sPIZZA___000',
}

export enum EasterEggType {
  // 顺序伐木
  ORDER_TREE = 'order_tree',
  // 打地鼠
  WHACK_A_MOLE = 'whack_a_mole',
  // 披萨狂潮
  PIZZA_RUSH = 'pizza_rush',
}

export enum CHAIN_TYPE_ENUM {
  ON_CHAIN = 'onChain',
  UNDER_CHAIN = 'underChain',
}

export enum PetFeature {
  // 砍树
  AXE = 'Axe',
  // 挖矿
  PICKAXE = 'Pickaxe',
  // 钓鱼
  FISHING_POLE = 'FishingPole',
}
