import { FATHER_TYPE_ENUM, IFatherInscription, PATH_ID_ENUM } from './type';
import ActionIcon from '../public/image/menus/action.svg';
import HairIcon from '../public/image/menus/hair.svg';
import PantsIcon from '../public/image/menus/pants.svg';
import ShirtIcon from '../public/image/menus/shirt.svg';
import ShoesIcon from '../public/image/menus/shoes.svg';
import PetIcon from '../public/image/menus/pet.svg';
import { SVG_FILE } from './staticFile';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

// 如果为true，表示使用本地资源，上线之前改为false
export const IS_LOCAL_TEST = process.env.USE_CONFIG === 'LOCAL_TEST_CONFIG';

//如果 AVATAR_VERSION参数未定义则是 浏览器版本
export const IS_AVATAR_PAGE = typeof process.env.AVATAR_VERSION === 'undefined';

// nft图浏览器接口地址 目前用在贴图
export const ORD_NFT_IMG_SERVER: string = process.env.ORD_NFT_IMG_SERVER as string;

// 当前是否为测试环境
export const IS_TEST_ENV =
  process.env.ENVIRONMENT !== 'online' &&
  process.env.ENVIRONMENT !== 'release' &&
  process.env.ENVIRONMENT !== 'exp';

export const IS_OLD_API =
  process.env.ENVIRONMENT == 'online' ||
  process.env.ENVIRONMENT == 'release' ||
  process.env.ENVIRONMENT == 'pre';

// ord浏览器根域名
const ORD_SERVER = process.env.ORD_SERVER;
// ord浏览器根域名
const CDN_SERVER = process.env.CDN_SERVER || '';
const CDN_VERSION = process.env.CDN_VERSION || 'v0.0.0';

export const THEME_MEDIA_NUM = {
  ORD_BROWSER: IS_AVATAR_PAGE ? 600 : 0, //ordinals浏览器显示尺寸
  ORD_PREVIEW: IS_AVATAR_PAGE ? 301 : 0, //ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
  FRONTEND_LARGE: IS_AVATAR_PAGE ? 1294 : 1, //官网大屏显示
};
export const THEME_MEDIA_ENUM = {
  ORD_BROWSER: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_BROWSER}px)`, //ordinals浏览器显示尺寸
  ORD_PREVIEW: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_PREVIEW}px)`, //ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
  FRONTEND_LARGE: `@media screen and (min-width: ${THEME_MEDIA_NUM.FRONTEND_LARGE}px)`, //官网大屏显示
};

function loadAvatarModel(element: IFatherInscription, loaded: () => void) {
  let typeName = String(element.type);
  if (typeName === FATHER_TYPE_ENUM.Hat) {
    typeName = 'Heat';
  }

  let index = element.childrenInscription.length + 1;
  const iconPath =
    `./assets/${typeName}/icon_${typeName}_` + index.toString().padStart(2, '0') + `.png`;
  const glbPath = `./assets/${typeName}/${typeName}_` + index.toString().padStart(2, '0') + `.glb`;
  const loader = new GLTFLoader();

  loader.load(
    glbPath,
    (gltf) => {
      element.childrenInscription.push({
        inscriptionId: glbPath,
        metadata: {
          collection: 'uniworlds_' + typeName,
          version: 'v0.0.1',
          icon: iconPath,
        },
      });
      loadAvatarModel(element, loaded);
    },
    undefined,
    (error) => {
      console.error('no exist glb ', glbPath);
      loaded();
    }
  );
}

// 本地资源测试配置
const LOCAL_TEST_CONFIG = {
  // avatar铭文id
  AVATAR_INSCRIPTION_ID: {
    Body: './assets/MainBody.glb',
    Hat: './assets/Heat/Heat_00.glb',
    Pants: './assets/Pants/Pants_00.glb',
    Shirt: './assets/Shirt/Shirt_00.glb',
    Shoes: './assets/Shoes/Shoes_00.glb',
  },
  // 铭文配置
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: ActionIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: PetIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: PantsIcon.src,
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.pantsTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.pantsColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: HairIcon.src,
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.hatTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.hatColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: ShoesIcon.src,
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shoesTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shoesColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: ShirtIcon.src,
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
  ] as IFatherInscription[],
};
// 本地链测试链配置
const CHAIN_TEST_CONFIG = {
  AVATAR_INSCRIPTION_ID: {
    Body: '25208027eb4130ea6ed1b9a0ac770c77c8350de3114d19411a8f5da9a3dff5ebi0',
    Hat: '7fac2a60f8608ba932f04256989d609c7f53dab665a273f9fb102b547f37b8d3i0',
    Pants: 'e48963a104b4c7aaa70a3dc490e4a587ced5d552da63123b6ce94c58bc718570i0',
    Shirt: 'f1ced2bb3a720d27ff8702130ec2fe3c96ea54a1d6ce0ed889406a751ee7a92fi0',
    Shoes: '801d00c4a31f753e66209ca8c5c4237ea40f21d57a92989ff9b986c982dc317ei0',
  },
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '016485e67d88cf65fe90df9186388b9db6bf6291fea9215b7241fd817b74fc2di0', // 父铭文id
      childrenInscription: [], //子铭文 会去查父铭文下的所有子铭文
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: '87a6d9ade40333993c3311e025121ff491b752ba39d38dcdf6ccde9f54545fcdi0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.pantsTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.pantsColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: '62766e954248db0d71140550bb9cc28b64378492aba7336e32c67ae7c5020beai0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.hatTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.hatColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: '2f98e7d9e0854cc7867d9c60a130fe732734fdd6087f53b7e795040d3274b7b9i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shoesTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shoesColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Gloves,
      pathIdKey: PATH_ID_ENUM.glovesId,
      inscriptionId: 'f285673478bcc0665803cb79dabc574b852cc01e71463a8ae5e264278609ceb5i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: '82e4476dfc352cb67f468df7916be47b0fde51a5f1f56f31b8c51e54420af760i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '14b7567212e718889c619f6967d6d6b8c90699b7e68f906dca13da38900d7b87i0',
      childrenInscription: [],
    },
    // {
    //   type: FATHER_TYPE_ENUM.ShirtTexture,
    //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
    //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
    //   childrenInscription: []
    // }
  ] as IFatherInscription[],
};
// 线上正式环境配置
const CHAIN_PRO_CONFIG = {
  TEST_USER_AVATAR_INSCRIPTION_ID: '', //置空
  AVATAR_INSCRIPTION_ID: {
    Body: '6c3141cf57bd1a24ed4da713a81d3246ec8ab1a692a0197bd8afdb71f24683adi0',
    Hat: '204c15c86a05e281dc946f12aa446ed5cc3c6de49c1e071651ed8a415e95bb2bi0',
    Pants: 'ca7f24e71dbd80782951118fd41326fd8a27f475ffbac97f7ff61c3a75debdeci0',
    Shirt: '74755608a996fef319c1f24067458c0d4ac9c665045dbc95c8660c9edcb0f9f3i0',
    Shoes: '511664b138590d543b2e5cc5329ee5b13c66f5dd18b81986f3ab6b5dcc0d629ei0',
  },
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: 'b8c45e58b6ef2d6115147d7fb854bf109ed385311116452def5ec92116987784i0', // 父铭文id
      childrenInscription: [], //子铭文 会去查父铭文下的所有子铭文
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: '43735105faacec9c619cb0ae2164dcc7363c2d501a9dc3b2cccee148abdf7351i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.pantsTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.pantsColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: '1a305cbb48d90c65a1970d71a300519aa534ffcbb6bd6fe63d08fe6bad740c9fi0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.hatTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.hatColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: 'b26360e7fd46ceac87b359be31645964c3850694f6fff6b9902086329e782885i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shoesTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shoesColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Gloves,
      pathIdKey: PATH_ID_ENUM.glovesId,
      // TODO: 手套铭文上链之后更新下方铭文
      inscriptionId: 'b40c8301d61d907f6477a2f0de7c172642e4adf95a44d15059ddac1dbf76a694i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: '55cfedf9503051973096f73d97a322500b465756bf86b015b3ce830c2dc6e09ci0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '5a0dbb6ec28ecbd3e07bd466ea1c890bf09a4b49452e6b171bd68ace3fbe8a50i0',
      childrenInscription: [],
    },
    // {
    //   type: FATHER_TYPE_ENUM.ShirtTexture,
    //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
    //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
    //   childrenInscription: []
    // }
  ] as IFatherInscription[],
};

// 此配置为装饰副配置，例如 贴图 颜色，目的是为了排除渲染avatar时不把副配置当作主装饰使用----后续有新的副配置需要手动添加到这里，否则可能渲染报错
export const EXCLUDE_APPENDIX_PATH_ID = [PATH_ID_ENUM.shirtTextureId, PATH_ID_ENUM.shirtColor];

// 菜单分组
export const MENU_GROUP = [
  {
    title: 'Clothes Group',
    icon: SVG_FILE.shirtSelectIcon,
    unSelectIcon: SVG_FILE.shirtUnselectIcon,
    fathersType: [
      FATHER_TYPE_ENUM.Shirt,
      FATHER_TYPE_ENUM.Pants,
      FATHER_TYPE_ENUM.Shoes,
      FATHER_TYPE_ENUM.Hat,
    ], //匹配CONFIG中的type
  },
  {
    title: 'Hair Group',
    icon: SVG_FILE.kingSelectIcon,
    unSelectIcon: SVG_FILE.kingUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Gloves],
  },
  {
    title: 'Pet Group',
    icon: SVG_FILE.petSelectIcon,
    unSelectIcon: SVG_FILE.petUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Pet],
  },
  {
    title: 'Action Group',
    icon: SVG_FILE.actionSelectIcon,
    unSelectIcon: SVG_FILE.actionUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Action],
  },
];

const CONFIG = (() => {
  switch (process.env.USE_CONFIG as string) {
    case 'CHAIN_PRO_CONFIG':
      return CHAIN_PRO_CONFIG;
    case 'CHAIN_TEST_CONFIG':
      return CHAIN_TEST_CONFIG;
    case 'LOCAL_TEST_CONFIG':
      return LOCAL_TEST_CONFIG;
  }
  throw new Error('USE_CONFIG value is not support');
})();

const { AVATAR_INSCRIPTION_ID, DEFAULT_FATHER_INSCRIPTION } = CONFIG;
export {
  ORD_SERVER,
  CDN_SERVER,
  CDN_VERSION,
  AVATAR_INSCRIPTION_ID,
  DEFAULT_FATHER_INSCRIPTION,
  loadAvatarModel,
};
