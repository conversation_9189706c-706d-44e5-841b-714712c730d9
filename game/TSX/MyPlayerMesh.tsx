import HandItem, { HandItemDetail } from '@/game/TSX/HandItem';
import { useSelector } from 'react-redux';
import { IAppState, SCENE_TYPE } from '@/constant/type';
import { useFrame, useThree } from '@react-three/fiber';
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { Raycaster } from 'three/src/Three.Core';
import { useNetWork } from '@/game/TS/useNetWork';
import { Entity } from '@/game/TS/Entity/Entity';
import { UseGameState } from '@/game_lib/stores/useGame';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import { EntityPizzaMesh } from '@/game/TS/Entity/Components/EntityPizzaMesh';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import { getLocalSessionId, toFormatAccount } from '@/utils';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import { PizzaPointConfig } from '@/game/Config/PizzaPointConfig';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  TransformData,
} from '@/game/Global/GlobalSpaceEvent';
import md5 from 'md5';
import { MapConfig } from '@/game/Config/MapConfig';
import GlobalSpace from '@/game/Global/GlobalSpace';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';

export default function CreateEntity({
  avatarData,
  itemDetail,
}: {
  avatarData: IAvatarMetadata;
  itemDetail: HandItemDetail;
}) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const { scene } = useThree();
  const playerRef = useRef<THREE.Group>(null);
  // 创建Raycaster实例
  const raycasterRef = useRef<Raycaster>(new Raycaster());
  const transformPositionRef = useRef<THREE.Vector3>(new THREE.Vector3());
  const directionRef = useRef<THREE.Vector3>(new THREE.Vector3(0, -1, 0));

  const { sendMapId, sendMapPosition } = useNetWork();
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
  const [isConnect, setIsConnected] = useState<boolean>(false);
  const [pizzaTick, setPizzaTick] = useState<string>('');

  const [playerEntity, setPlayerEntity] = useState<Entity | null>(null);
  const isWeary = useGame((state: UseGameState) => state.isWeary);
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const animationSet = useGame((state: UseGameState) => state.animationSet);
  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);
  const oldAnimationRef = useRef<string>('');

  useEffect(() => {
    initializeAnimationSet({
      idle: 'idle',
      walk: 'walk',
      run: 'run',
      jump: 'jump_01',
      jumpIdle: 'jump_02',
      jumpLand: 'jump_03',
      fall: 'fall',
    });
  }, [initializeAnimationSet]);

  useEffect(() => {
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.AvatarMesh,
      ComponentType.TopText,
      ComponentType.Face,
      ComponentType.ChatBubble,
    ]);
    entity.name = 'MyPlayerMesh';
    myPlayer.clientEntityIndex = entity.clientIndex;
    const faceComponent = entity.getComponent<EntityFace>(ComponentType.Face);
    if (faceComponent) {
      faceComponent.setFaceData('Face', 'avatar');
    }
    const animationComponent = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animationComponent) {
      animationComponent.setAnimationRename('idle', 'Action_00');
      animationComponent.setAnimationRename('run', 'Action_01');
      animationComponent.setAnimationRename('walk', 'Action_03');
      animationComponent.setAnimationRename('weary_idle', 'Action_33');
      animationComponent.setAnimationRename('weary_walk', 'Action_34');
      animationComponent.setJumpAnimation('Action_04');
      animationComponent.setDefaultAnimation('idle');
    }
    const entityPizza = EntityManager.getInstance().createClientEntity([
      ComponentType.FollowBone,
      ComponentType.PizzaMesh,
    ]);
    entityPizza.name = 'PizzaMesh';

    const followBoneComponent = entityPizza.getComponent<EntityFollowBone>(
      ComponentType.FollowBone
    );
    if (followBoneComponent) {
      followBoneComponent.setTarget(entity.clientIndex, 'Bip001_Spine1');
    }

    myPlayer.watchPizzaChange((count, tick) => {
      setPizzaTick(tick);
      const pizzaComponent = entityPizza.getComponent<EntityPizzaMesh>(ComponentType.PizzaMesh);
      if (pizzaComponent) {
        pizzaComponent.setTick(tick);
        pizzaComponent.setPizzaCount(count);
      }
    });

    setPlayerEntity(entity);
    return () => {
      EntityManager.getInstance().removeEntity(entity);
    };
  }, []);

  useEffect(() => {
    if (!playerEntity) return;
    const topTextComponent = playerEntity.getComponent<EntityTopText>(ComponentType.TopText);
    if (topTextComponent) {
      topTextComponent.setHideMyself(true);
      topTextComponent.setText(toFormatAccount(btcAddress));
    }
  }, [playerEntity, btcAddress]);

  useEffect(() => {
    if (!playerEntity) return;
    const animationComponent = playerEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animationComponent) {
      const replaceAnimations: string[] = [];
      animationSet.idle && replaceAnimations.push(animationSet.idle);
      animationSet.wearyIdle && replaceAnimations.push(animationSet.wearyIdle);
      animationSet.wearyWalk && replaceAnimations.push(animationSet.wearyWalk);
      animationSet.jumpLand && replaceAnimations.push(animationSet.jumpLand);
      animationSet.run && replaceAnimations.push(animationSet.run);
      animationSet.walk && replaceAnimations.push(animationSet.walk);
      animationComponent.setReplaceAnimations(replaceAnimations, true);
      animationComponent.setDefaultAnimation(
        (isWeary ? animationSet.wearyIdle : animationSet.idle) || 'idle'
      );
    }
  }, [playerEntity, animationSet, isWeary]);

  useEffect(() => {
    if (!playerEntity) return;
    const avatarComponent = playerEntity.getComponent<EntityAvatarMesh>(ComponentType.AvatarMesh);
    if (avatarComponent) {
      if (pizzaTick.length > 0) {
        const tempAvatar = { ...avatarData };
        tempAvatar.hatId = PizzaPointConfig.getInstance().getPizzaBagUrl(pizzaTick);
        avatarComponent.setAvatar(tempAvatar);
      } else {
        avatarComponent.setAvatar(avatarData);
      }
    }
  }, [pizzaTick, playerEntity, avatarData]);

  useEffect(() => {
    if (!playerEntity) return;
    const animationComponent = playerEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animationComponent) {
      animationComponent.playAnimation(curAnimation || 'idle');
      oldAnimationRef.current = curAnimation || 'idle';
    }
  }, [playerEntity, curAnimation]);

  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
      GlobalDataKey.SceneType,
      (type) => {
        setSceneType(type);
      }
    );

    const connectKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.IsSocketConnected,
      (value) => {
        setIsConnected(value);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, unsubscribe);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.IsSocketConnected, connectKey);
    };
  }, []);

  useEffect(() => {
    if (sceneType === SCENE_TYPE.None) return;
    if (!isConnect) return;
    const session = getLocalSessionId(myPlayer.btcAddress);
    sendMapId(md5(sceneType + session));
    const interval = setInterval(() => {
      const start = Math.floor(Math.random() * 8) + 1;
      const length = Math.floor(Math.random() * 8) + 12;
      const posX = Math.floor(myPlayer.position.x * 10);
      const posY = Math.floor(myPlayer.position.y * 10);
      const posZ = Math.floor(myPlayer.position.z * 10);
      //截取范围
      const posMd5 = start + md5(posX + session + 'pos').slice(start, start + length);
      const positionList = [String(posX), String(posY), String(posZ), posMd5];
      sendMapPosition(positionList);
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, [sendMapId, sendMapPosition, sceneType, myPlayer, isConnect]);

  useEffect(() => {
    if (!playerEntity) return;
    myPlayer.setAppApi(AppGameApiKey.sendChat, (content: string) => {
      const chatBubble = playerEntity.getComponent<EntityChatBubble>(ComponentType.ChatBubble);
      if (chatBubble) {
        const endTime = Date.now() + 10000;
        chatBubble.setChat(content, endTime);
      }
    });
  }, [myPlayer, playerEntity]);

  useFrame(() => {
    if (!playerRef.current) return;
    if (!playerEntity) return;
    if (oldAnimationRef.current.length > 0) {
      //反向同步动作到 useGame
      const animationComponent = playerEntity.getComponent<EntityAnimation>(
        ComponentType.Animation
      );
      if (animationComponent) {
        if (!animationComponent.isSameAnimation(oldAnimationRef.current)) {
          setCurAnimation(animationComponent.curAnimation);
        }
      }
    }
    const oldPosition = { x: myPlayer.position.x, y: myPlayer.position.y, z: myPlayer.position.z };
    playerRef.current.getWorldPosition(myPlayer.position);
    playerRef.current.getWorldQuaternion(myPlayer.quaternion);
    if (myPlayer.position.distanceTo(oldPosition) > 0.01) {
      myPlayer.hitTreeFinishCallback && myPlayer.hitTreeFinishCallback();
      myPlayer.hitStoneFinishCallback && myPlayer.hitStoneFinishCallback();
    }
    if (myPlayer.position.y < -8 || myPlayer.position.y > 10) {
      const curMapData = MapConfig.getInstance().getCurMapData();
      //地图没有完全加载出来的时候先不传送
      if (curMapData) {
        transformPositionRef.current.set(myPlayer.position.x, 2, myPlayer.position.z);

        // 从中心点到生成的点发射射线
        raycasterRef.current.set(transformPositionRef.current, directionRef.current);
        raycasterRef.current.far = 8; //多检测1的长度 , 给出生点留出体积

        //解决特效object导致的报错
        const list: THREE.Object3D[] = [];
        scene.traverse((value) => {
          if (value.type === 'Mesh') {
            list.push(value);
          }
        });
        // 获取射线与场景中物体的相交情况
        const intersects = raycasterRef.current.intersectObjects(list);

        if (intersects.length === 0) {
          transformPositionRef.current.x = curMapData.offset[0] || 0;
          transformPositionRef.current.z = curMapData.offset[2] || 0;
        }
        GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
          position: transformPositionRef.current,
          characterType: CharacterType.Player,
          sceneType: SCENE_TYPE.None,
        });
      }
    } else {
      GlobalSpace.updateCharacterPosition(myPlayer.position);
      if (playerEntity) {
        const transformComponent = playerEntity.getComponent<EntityTransform>(
          ComponentType.Transform
        );
        if (transformComponent && !transformComponent.forcePosition) {
          transformComponent.setRootObject(playerRef.current);
        }
      }
      myPlayer.checkAllMapPoint();
    }
  });
  return (
    <group position={[0, -0.92, 0]} ref={playerRef}>
      {/* Replace character model here */}
      {playerEntity && (
        <HandItem
          targetClientIndex={playerEntity.clientIndex}
          itemDetail={itemDetail}
          useGame={useGame}
        />
      )}
    </group>
  );
}
