import * as THREE from 'three';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { GLTF } from 'three-stdlib';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
// import {forEach} from "lodash";
import { forEach } from 'es-toolkit/compat';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter';

const jsonCache: Map<string, JSON> = new Map();
const glbBufferCache: Map<string, ArrayBuffer> = new Map();
const audioCache: Map<string, AudioBuffer> = new Map();
const loadingCache: Map<string, boolean> = new Map();

class GlobalLoaderUtil {
  private jsonLoader: THREE.FileLoader;
  private glbLoader: GLTFLoader;
  private audioLoader: THREE.AudioLoader;

  constructor() {
    this.jsonLoader = new THREE.FileLoader(); // json文本加载器
    this.jsonLoader.setResponseType('text');

    this.glbLoader = new GLTFLoader();

    this.audioLoader = new THREE.AudioLoader();
  }

  loadJson(url: string, cb: (json: JSON) => void) {
    const json = jsonCache.get(url);
    if (json) {
      cb(json);
      return;
    }

    if (loadingCache.has(url)) {
      setTimeout(() => {
        this.loadJson(url, cb);
      }, 50);
      return;
    }
    loadingCache.set(url, true);
    this.jsonLoader.load(getCdnLink(url), (result) => {
      const jsonObj = JSON.parse(result as any);
      loadingCache.delete(url);
      jsonCache.set(url, jsonObj);
      cb(jsonObj);
    });
  }

  async loadJsonPromise(url: string) {
    return new Promise((resolve, reject) => {
      this.loadJson(url, (json) => {
        resolve(json);
      });
    });
  }

  loadGlb(url: string, cb: (json: GLTF) => void) {
    const glbBuffer = glbBufferCache.get(url);
    if (glbBuffer) {
      this.glbLoader.parse(glbBuffer, '', (result) => {
        cb(result as any);
      });
      return;
    }

    if (loadingCache.has(url)) {
      setTimeout(() => {
        this.loadGlb(url, cb);
      }, 50);
      return;
    }
    loadingCache.set(url, true);
    this.glbLoader.load(getCdnLink(url), (result) => {
      const exporter = new GLTFExporter();
      exporter.parse(
        result.scene,
        (result) => {
          loadingCache.delete(url);
          glbBufferCache.set(url, result as ArrayBuffer);
          this.loadGlb(url, cb);
        },
        (error) => {
          console.error('An error occurred during GLTF export:', error);
        },
        { binary: true, animations: result.animations }
      );
      // cb(result as any);
    });
  }

  loadAudio(url: string, cb: (buffer: AudioBuffer) => void) {
    const buffer = audioCache.get(url);
    if (buffer) {
      cb(buffer);
      return;
    }

    if (loadingCache.has(url)) {
      setTimeout(() => {
        this.loadAudio(url, cb);
      }, 50);
      return;
    }
    loadingCache.set(url, true);
    this.audioLoader.load(getCdnLink(url), (result) => {
      audioCache.set(url, result);
      loadingCache.delete(url);
      cb(result);
    });
  }
}

export const LoaderUtil = new GlobalLoaderUtil();

let isLoadGlb = true;

export function preloadItemGlb() {
  if (isLoadGlb) {
    return;
  }
  isLoadGlb = true;
  LoaderUtil.loadGlb('./assets/Prop/Prop_00.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_01.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_02.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_03.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_04.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_05.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Prop_06.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t0_01.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t0_02.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t1_01.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t1_02.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t2_01.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/pickaxe_t2_02.glb', () => {});
  LoaderUtil.loadGlb('./assets/Prop/Action_fishing_01.glb', (gltf) => {});
}

export function preloadParticleJson() {
  // LoaderUtil.loadJson('./particles/Effect_wood_0.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_wood_1.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_smoke.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_broken.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_stone_0.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_stone_1.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_smoke_stone.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_water_splash.json', (json) => {});
  // LoaderUtil.loadJson('./particles/Effect_water_splash_01.json', (json) => {});
}

let isLoadAudio = true;

export function preloadAudio() {
  if (isLoadAudio) {
    return;
  }
  isLoadAudio = true;
  const loadList = [
    './sound/chop/chop_tree_small_1.mp3',
    './sound/chop/chop_tree_small_2.mp3',
    './sound/chop/chop_tree_small_3.mp3',
    './sound/chop/chop_tree_medium_1.mp3',
    './sound/chop/chop_tree_medium_2.mp3',
    './sound/chop/chop_tree_medium_3.mp3',
    './sound/chop/chop_tree_large_1.mp3',
    './sound/chop/chop_tree_large_2.mp3',
    './sound/chop/chop_tree_large_3.mp3',
    './sound/fall/tree_trunk_large.mp3',
    './sound/fall/tree_trunk_medium.mp3',
    './sound/fall/tree_trunk_small.mp3',
    './sound/break/axe_damage.mp3',
    './sound/reward/reward_axe.mp3',
    './sound/fall/stone_broken_small.mp3',
    './sound/fall/stone_broken_medium.mp3',
    './sound/fall/stone_broken_large.mp3',
    './sound/chop/mine_stone_wave.mp3',
    './sound/rod/rod_cast.mp3',
    './sound/rod/rod_reel.mp3',
    './sound/rod/rod_float.mp3',
    './sound/fish/fish_bite_small.mp3',
    './sound/fish/fish_bite_big.mp3',
    './sound/fish/fish_struggle.mp3',
  ];

  forEach(loadList, (url) => {
    LoaderUtil.loadAudio(url, () => {});
  });
}
