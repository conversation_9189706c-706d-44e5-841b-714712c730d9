/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { NpcConfig, NpcData, NpcType } from '@/game/Config/NpcConfig';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import { forEach } from 'es-toolkit/compat';
import { NpcChatConfig, NpcChatData } from '@/game/Config/NpcChatConfig';
import {
  NpcChatOptionConfig,
  NpcChatOptionData,
  NpcChatOptionType,
} from '@/game/Config/NpcChatOptionConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { useAppDispatch } from '@/hooks/useStore';
import { updateModalState } from '@/store/modal';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { ItemConfig, ItemData } from '@/game/Config/ItemConfig';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { IS_RELEASE_ONLINE } from '@/constant';

const PIZZA_NPC_ID = 2003;

export default function Npc({ npcId }: { npcId: number }) {
  const particleSystem = getParticleSystem();
  const { userBasicInfo } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [npcData, setNpcData] = useState<NpcData | null>(null);
  const [npcChatData, setNpcChatData] = useState<NpcChatData | null>(null);
  const [npcChatOptionList, setNpcChatOptionList] = useState<NpcChatOptionData[]>([]);
  const [npcEntity, setNpcEntity] = useState<Entity | null>(null);
  const [handItemEntity, setHandItemEntity] = useState<Entity | null>(null);
  const [handItemData, setHandItemData] = useState<ItemData | null>(null);
  const [showChat, setShowChat] = useState(true);
  const [isPizzaTime, setIsPizzaTime] = useState(false);
  const [nearNpc, setNearNpc] = useState(false);
  const myPlayer = GetMyPlayer();
  const dispatch = useAppDispatch();
  const [haveTwitter, setHaveTwitter] = useState<boolean>(false);

  const [dailyBuyTotalCount, setDailyBuyTotalCount] = useState(0);
  const [dailyCurrentBuyCount, setDailyCurrentBuyCount] = useState(0);

  const resetNpcChatData = () => {
    if (npcData) {
      switch (npcData.type) {
        case NpcType.NormalNpc:
          NpcChatConfig.getInstance().getData(npcData.chatId1, (data) => {
            setNpcChatData(data);
          });
          break;
        case NpcType.TwitterNpc:
          // TODO: remove next line after pet version release
          if (IS_RELEASE_ONLINE) {
            if (haveTwitter) {
              NpcChatConfig.getInstance().getData(npcData.chatId2, (data) => {
                setNpcChatData(data);
              });
            } else {
              NpcChatConfig.getInstance().getData(npcData.chatId1, (data) => {
                setNpcChatData(data);
              });
            }
          } else {
            if (myPlayer.btcAddress) {
              NpcChatConfig.getInstance().getData(npcData.chatId2, (data) => {
                setNpcChatData(data);
              });
            } else {
              NpcChatConfig.getInstance().getData(npcData.chatId1, (data) => {
                setNpcChatData(data);
              });
            }
          }
          break;
      }
    }
  };

  useEffect(() => {
    const pointKey = 'npc_' + npcId;
    myPlayer.unregisterMapPoint(pointKey);
    if (npcId === PIZZA_NPC_ID && !isPizzaTime) {
      setNpcData(null);
      return;
    }
    NpcConfig.getInstance().getData(npcId, (data) => {
      setNpcData(data);
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(data.position[0], data.position[1], data.position[2]),
        (distance) => {
          setNearNpc(distance < data.distance);
        }
      );
    });
  }, [isPizzaTime, npcId]);

  useEffect(() => {
    if (npcData === null) {
      return;
    }

    const componentList = [
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.ChatBubble,
    ];
    if (npcData.glbUrl.length > 0) {
      componentList.push(ComponentType.GlbMesh);
    } else {
      componentList.push(ComponentType.AvatarMesh);
      componentList.push(ComponentType.Face);
    }
    const entity = EntityManager.getInstance().createClientEntity(componentList, true);
    entity.name = 'npc_' + npcId;
    const glbMesh = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    if (glbMesh) {
      glbMesh.setYawOffset(npcData.yawOffset);
      glbMesh.setGlbUrl(npcData.glbUrl);
      glbMesh.setScale(npcData.glbScale);
    }
    const avatarMesh = entity.getComponent<EntityAvatarMesh>(ComponentType.AvatarMesh);
    if (avatarMesh) {
      avatarMesh.setAvatar(npcData.avatarData);
      avatarMesh.setScale(npcData.glbScale);
    }
    const avatarFace = entity.getComponent<EntityFace>(ComponentType.Face);
    if (avatarFace) {
      if (npcData.faceUrl.length > 0) {
        avatarFace.setFaceData('Face', 'npc');
        avatarFace.setFaceUrl(npcData.faceUrl);
      } else {
        avatarFace.setFaceData('Face', 'avatar');
      }
    }
    const transform = entity.getComponent<EntityTransform>(ComponentType.Transform);
    if (transform) {
      const quaternion = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(0, (npcData.yawY * Math.PI) / 180, 0)
      );
      transform.setPosition(
        new THREE.Vector3(npcData.position[0], npcData.position[1], npcData.position[2]),
        quaternion
      );
    }
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animation) {
      animation.setDefaultAnimation(npcData.default_action);
      animation.setReplaceAnimations([npcData.default_action]);
      animation.setAnimationRename('idle', 'Action_00');
      animation.playAnimation(npcData.default_action);
    }
    ItemConfig.getInstance().getData(npcData.handItemId, (data) => {
      setHandItemData(data);
    });
    // playerData.yawOffset = npcData.yawOffset;
    setNpcEntity(entity);
    return () => {
      setNpcEntity(null);
      EntityManager.getInstance().removeEntity(entity);
    };
  }, [npcData]);

  useEffect(() => {
    if (handItemData && npcEntity) {
      const entity = EntityManager.getInstance().createClientEntity(
        [ComponentType.FollowBone, ComponentType.GlbMesh],
        true
      );
      entity.name = npcEntity.name + ' hand item';
      const glbMesh = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
      if (glbMesh) {
        glbMesh.setGlbUrl(handItemData.glb_url);
      }
      const followBone = entity.getComponent<EntityFollowBone>(ComponentType.FollowBone);
      if (followBone) {
        followBone.setTarget(npcEntity.clientIndex, 'Bip001_R_Hand');
        followBone.setPosition(
          new THREE.Vector3(
            handItemData.position[0],
            handItemData.position[1],
            handItemData.position[2]
          ),
          new THREE.Quaternion().setFromEuler(
            new THREE.Euler(
              (handItemData.rotation[0] * Math.PI) / 180,
              (handItemData.rotation[1] * Math.PI) / 180,
              (handItemData.rotation[2] * Math.PI) / 180
            )
          )
        );
      }
      setHandItemEntity(entity);
      return () => {
        setHandItemEntity(null);
        EntityManager.getInstance().removeEntity(entity);
      };
    }
  }, [npcEntity, handItemData]);

  useEffect(() => {
    if (nearNpc) {
      resetNpcChatData();
    } else {
      setNpcChatData(null);
    }
  }, [npcData, haveTwitter, nearNpc]);

  useEffect(() => {
    if (npcChatData) {
      NpcChatOptionConfig.getInstance().getDataList(npcChatData.optionList, (optionList) => {
        setNpcChatOptionList(optionList);
      });
    } else {
      setNpcChatOptionList([]);
    }
  }, [npcChatData]);

  useEffect(() => {
    const transform = npcEntity?.getComponent<EntityTransform>(ComponentType.Transform);
    const animation = npcEntity?.getComponent<EntityAnimation>(ComponentType.Animation);
    const chatBubble = npcEntity?.getComponent<EntityChatBubble>(ComponentType.ChatBubble);
    if (npcData && transform && animation && chatBubble) {
      const npcChat = () => {
        if (npcChatData) {
          transform.faceToPosition(myPlayer.position);
          if (npcChatData.replayAction === 1) {
            animation.setDefaultAnimation(npcChatData.chatAction);
            animation.playAnimation(npcChatData.chatAction);
          } else {
            animation.playAnimation(npcChatData.chatAction);
          }
          const task = chatBubble.createAnswer();
          setTimeout(() => {
            const content = NpcChatConfig.getInstance().getWord(npcChatData);
            task.start(
              {
                content: content,
                soundUrl: npcChatData.chatUrl.length > 3 ? getCdnLink(npcChatData.chatUrl) : '',
                citationFiles: [],
              },
              () => {
                //  otherPlayerData.clearAnswer()
              }
            );
          }, 1);
        }
      };
      if (nearNpc) {
        npcChat();
      } else {
        animation.setDefaultAnimation(npcData.default_action);
        // animation.playAnimation(npcData.default_action);
        if (animation.playAnimation(npcData.default_action) && npcData.lockYaw) {
          transform.setPosition(
            new THREE.Vector3(npcData.position[0], npcData.position[1], npcData.position[2]),
            new THREE.Quaternion().setFromEuler(
              new THREE.Euler(0, (npcData.yawY * Math.PI) / 180, 0)
            )
          );
        }
        chatBubble.clearAnswer();
      }
    }
  }, [npcEntity, nearNpc, npcChatData]);

  useEffect(() => {
    if (myPlayer.UsePetInscriptionId.length > 0) {
      return;
    }
    if (npcData === null) return;

    if (!nearNpc) {
      return;
    }
    const animation = npcEntity?.getComponent<EntityAnimation>(ComponentType.Animation);
    const transform = npcEntity?.getComponent<EntityTransform>(ComponentType.Transform);
    const itemGlbMesh = handItemEntity?.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);

    if (animation && transform && showChat && npcChatOptionList.length > 0) {
      const lockButton = (delay = 2000) => {
        setShowChat(false);
        setTimeout(() => {
          setShowChat(true);
        }, delay);
      };
      const opKeyList: string[] = [];

      npcChatOptionList.forEach((option) => {
        let content = option.text;
        const params = option.params;
        switch (option.type) {
          case NpcChatOptionType.BuyEnergy:
            content += ' (' + dailyCurrentBuyCount + '/' + dailyBuyTotalCount + ')';
            break;
          // case NpcChatOptionType.OpenSynthesis:
          //   content += ' (' + workbenchCurrentCount + '/' + workbenchTotalCount + ')';
          //   break;
          case NpcChatOptionType.JoinActivity:
            const pizzaActivity = getPizzaActivity();
            const activityData = pizzaActivity.getActivityData();
            const now = Date.now();
            if (activityData.signUpTime - now > 0) {
              content +=
                '(Starts in ' + Math.floor((activityData.startTime - now) / 1000 / 60) + ' min)';
            } else if (activityData.endTime - now > 0) {
              if (activityData.pizzaData.length > 0) {
                content += '(Already registered)';
              } else {
                content += '(Not registered)';
              }
            }
            break;
        }
        opKeyList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              let needReset = true;
              switch (option.type) {
                case NpcChatOptionType.JumpChat:
                  const nextChatId = Number(params[0]);
                  setNpcChatData(null);
                  setNpcChatOptionList([]);
                  NpcChatConfig.getInstance().getData(nextChatId, (data) => {
                    setNpcChatData(data);
                  });
                  needReset = false;
                  break;
                case NpcChatOptionType.Twitter:
                  myPlayer.callAppApi(AppGameApiKey.authTwitter);
                  break;
                case NpcChatOptionType.ReceiveTool:
                  myPlayer.callAppApi(AppGameApiKey.receiveTool);
                  break;
                case NpcChatOptionType.BuyEnergy:
                  const buyCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.buyEnergy, buyCommunity);
                  break;
                case NpcChatOptionType.ActivityRule:
                  const activityType = Number(params[0]);
                  myPlayer.callAppApi(AppGameApiKey.activityRule, activityType);
                  break;
                case NpcChatOptionType.SubmitResources:
                  const submitCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.submitResources, submitCommunity);
                  break;
                case NpcChatOptionType.ShareTwitter:
                  const tweetsId = params[0];
                  myPlayer.callAppApi(AppGameApiKey.shareTweets, tweetsId);
                  break;
                case NpcChatOptionType.Donation:
                  const donationCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.donateTokens, donationCommunity);
                  break;
                case NpcChatOptionType.ClaimDrop:
                  const claimCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.claimDrop, claimCommunity);
                  break;
                case NpcChatOptionType.OpenSynthesis:
                  const synthesisType = params[0];
                  switch (synthesisType) {
                    case 'pet':
                      dispatch(
                        updateModalState({
                          petSynthesisModalConfig: {
                            isOpen: true,
                            confirmCallback: () => {
                              transform.setPosition(
                                new THREE.Vector3(-158.88, 0.01, -0.95),
                                new THREE.Quaternion(0, 0, 0, 1)
                              );
                              transform.faceToPosition(new THREE.Vector3(-159.18, 0, 0.6));
                              animation.setReplaceAnimations(['Action_39']);
                              animation.playAnimation('Action_39');
                              particleSystem.addParticle(
                                new THREE.Vector3(-159, 0.7, -0.28),
                                new THREE.Quaternion(0, 0, 0, 1),
                                './particles/Effect_PetRoom_work.json',
                                1,
                                3000,
                                true
                              );
                              animation.lockAnimation(3000);
                              itemGlbMesh?.setGlbUrl('');
                              setTimeout(() => {
                                if (animation.playAnimation(animation.defaultAnimation)) {
                                  itemGlbMesh?.setGlbUrl(handItemData?.glb_url || '');
                                  transform.setPosition(
                                    new THREE.Vector3(
                                      npcData.position[0],
                                      npcData.position[1],
                                      npcData.position[2]
                                    ),
                                    new THREE.Quaternion().setFromEuler(
                                      new THREE.Euler(0, npcData.yawY * (Math.PI / 180), 0)
                                    )
                                  );
                                }
                              }, 3000);
                            },
                          },
                        })
                      );
                      break;
                    case 'tool':
                    default:
                      dispatch(
                        updateModalState({
                          toolSynthesisModalConfig: {
                            isOpen: true,
                            confirmCallback: () => {
                              transform.setPosition(
                                new THREE.Vector3(
                                  npcData.position[0],
                                  npcData.position[1],
                                  npcData.position[2]
                                ),
                                new THREE.Quaternion().setFromEuler(
                                  new THREE.Euler(0, npcData.yawY * (Math.PI / 180), 0)
                                )
                              );
                              animation.playAnimation('Action_28');
                              animation.lockAnimation(3000);
                              setTimeout(() => {
                                animation.playAnimation(animation.defaultAnimation);
                              }, 3000);
                            },
                          },
                        })
                      );
                      break;
                  }
                  break;
                case NpcChatOptionType.JoinActivity:
                  const pizzaActivity = getPizzaActivity();
                  pizzaActivity.joinActivity();
                  break;
                case NpcChatOptionType.JumpLink:
                  const linkUrl = params[0];
                  if (linkUrl.length > 0) {
                    window.open(linkUrl);
                  }
                  break;
              }
              if (needReset) {
                resetNpcChatData();
              }
              lockButton(option.clickDelay);
            },
            option.id,
            content,
            getCdnLink(option.iconUrl)
          )
        );
      });

      return () => {
        forEach(opKeyList, (opKey) => {
          GlobalSpace.removeGameOp(opKey);
        });
      };
    }
  }, [
    npcEntity,
    handItemEntity,
    showChat,
    nearNpc,
    npcData,
    npcChatOptionList,
    dailyCurrentBuyCount,
    dailyBuyTotalCount,
  ]);

  useEffect(() => {
    if (userBasicInfo) {
      setDailyBuyTotalCount(userBasicInfo.toolConfig?.dailyBuyTotalCount || 0);
      setDailyCurrentBuyCount(userBasicInfo.toolConfig?.dailyCurrentBuyCount || 0);
      setHaveTwitter(userBasicInfo.twitterFlag || false);
    } else {
      setHaveTwitter(false);
    }
  }, [userBasicInfo]);

  useEffect(() => {
    if (npcId === PIZZA_NPC_ID) {
      const interval = setInterval(() => {
        const now = Date.now();
        const pizzaActivity = getPizzaActivity();
        const activityData = pizzaActivity.getActivityData();
        //报名中
        setIsPizzaTime(activityData.signUpTime < now && now <= activityData.startTime);
      }, 1000);
      return () => {
        clearInterval(interval);
      };
    }
  }, [npcId]);

  return null;
}
