import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { FishConfig, FishData, FishStatus } from '@/game/Config/FishConfig';
import { useFrame } from '@react-three/fiber';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import ParticleObject from '@/game/TSX/Particles/ParticleObject';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';

export default function SceneFishFloat({
  fishData,
  initPos,
}: {
  fishData: FishData;
  initPos: THREE.Vector3;
}) {
  const particleSystem = getParticleSystem();
  const fishFloatObject = FishConfig.getInstance().getObject(fishData.id);
  const groupRef = useRef<THREE.Group>(null);

  const [floatEntity, setFloatEntity] = useState<Entity | null>(null);

  const [fishStatus, setFishStatus] = useState<FishStatus>(FishStatus.Hide);

  useEffect(() => {
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.GlbMesh,
    ]);
    entity.name = 'fish float';
    const glbMeshComponent = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    if (glbMeshComponent) {
      glbMeshComponent.setGlbUrl('./assets/Prop/Action_fishing_01.glb');
      glbMeshComponent.setVisible(false, 'fish_shadow');
    }
    const transformComponent = entity.getComponent<EntityTransform>(ComponentType.Transform);
    if (transformComponent) {
      transformComponent.setPosition(
        initPos,
        new THREE.Quaternion().setFromEuler(new THREE.Euler(0, (90 / 180) * Math.PI, 0))
      );
    }
    const animationComponent = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animationComponent) {
      animationComponent.setReplaceAnimations(['Action_fish_02', 'Action_fish_03']);
      animationComponent.setDefaultAnimation('Action_fish_02');
    }
    setFloatEntity(entity);
    particleSystem.addParticle(
      initPos.clone(),
      new THREE.Quaternion(),
      './particles/Effect_water_splash.json',
      1,
      3000
    );
    if (groupRef.current) {
      groupRef.current.position.copy(initPos);
    }
    return () => {
      EntityManager.getInstance().removeEntity(entity);
    };
  }, [initPos]);

  useEffect(() => {
    if (!floatEntity) return;
    const animationComponent = floatEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    const glbMeshComponent = floatEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    if (animationComponent && glbMeshComponent) {
      switch (fishStatus) {
        case FishStatus.Hide:
          glbMeshComponent.setVisible(false, 'fish_shadow');
          return;
        case FishStatus.Show:
          glbMeshComponent.setVisible(true, 'fish_shadow');
          animationComponent.playAnimation('Action_fish_00');
          break;
        case FishStatus.Idle:
          animationComponent.playAnimation('Action_fish_02');
          break;
        case FishStatus.Bite:
          animationComponent.playAnimation('Action_fish_01', fishData.bite_speed);
          break;
        case FishStatus.Catch:
          animationComponent.playAnimation('Action_fish_03');
          break;
        default:
          animationComponent.playAnimation('Action_fish_02');
          break;
      }
    }
  }, [fishStatus, floatEntity]);

  useFrame((state) => {
    setFishStatus(fishFloatObject.status);
  });

  return (
    <>
      <group ref={groupRef}>
        {fishStatus == FishStatus.Catch && (
          <ParticleObject url={'./particles/Effect_water_splash_01.json'} scale={1} />
        )}
      </group>
    </>
  );
}
