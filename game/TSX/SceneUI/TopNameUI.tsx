import { Html } from '@react-three/drei';
import React from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';

import styled, { css } from 'styled-components';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';

const UIBox = styled.div`
  .bubble-container {
    position: relative;
    bottom: 0;
    pointer-events: none; /* 使鼠标事件穿透 */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    gap: 16px;
  }
`;

const StyledBubbleText = styled.div<{ $withBorder?: boolean; $showPetOnlyStyle?: boolean }>`
  position: relative;
  max-width: 900px;
  color: #ffffff;
  font-size: 35px;
  word-wrap: break-word;
  white-space: normal;
  user-select: none;
  pointer-events: none;
  box-sizing: border-box;
  filter: drop-shadow(1px 3px 1px black);

  ${({ $showPetOnlyStyle = false }) =>
    $showPetOnlyStyle &&
    css`
      font-size: 30px;
      font-weight: 400;
      letter-spacing: -0.64px;
      font-family: 'JetBrains Mono';
      text-align: right;
      height: 45px;
      padding: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 0 100px 100px 0;

      box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.25);
      padding-left: 30px;
      filter: none;

      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      background: var(--bgColorAlpha);
      border: 1px solid var(--bgColor);
    `}
  ${({ $withBorder = false }) =>
    $withBorder &&
    css`
      text-shadow: none;
      border: 1px solid rgba(0, 0, 0, 0.2);
      background: var(--bgColor);
      filter: none;
    `}
`;

const StyledAffinityTagWrapper = styled.div`
  width: 50px;
  height: 50px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.25));
  &::after {
    content: '';
    display: block;
    width: 40px;
    height: 40px;
    transform: rotate(45deg);
    flex-shrink: 0;
    border-radius: 8px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: -1;
    background:
      linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%),
      var(--bgColor);
  }
`;

const StyledAffinityTag = styled(SvgWrapper)`
  width: var(--iconSize);
  height: var(--iconSize);
  position: relative;
  transform: translate(var(--iconX), var(--iconY));
`;

export default function TopNameUI({
  textColor,
  height,
  name,
  topIconName,
  withBorder,
  showPetOnlyStyle,
  iconConfig,
}: {
  textColor: string;
  height: number;
  name: string;
  topIconName?: string;
  withBorder?: boolean;
  showPetOnlyStyle?: boolean;
  iconConfig: { x: number; y: number; size: number };
}) {
  const ref = React.useRef<THREE.Group>(null);
  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      const cameraWorldPos = new THREE.Vector3();
      camera.getWorldPosition(cameraWorldPos);
      const objWorldPos = new THREE.Vector3();
      ref.current.getWorldPosition(objWorldPos);
      cameraWorldPos.y = objWorldPos.y;
      ref.current.lookAt(cameraWorldPos);
    }
  });
  return (
    <group ref={ref} position={[0, height, 0]}>
      {name.length > 0 && (
        <Html
          distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
          transform // 保证HTML内容的大小不随距离变化
          // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
          pointerEvents="none" // 禁用鼠标事件
          position={[0, 0, 0]}
          style={{
            transformOrigin: 'bottom center',
            transform: 'translate(0,-50%)',
            transition: 'all 0.3s ease',
            left: '50%',
          }}
          center={false}>
          <UIBox>
            <div className="bubble-container">
              <StyledBubbleText
                className="bubble-text"
                style={{ '--bgColor': textColor, '--bgColorAlpha': textColor + '66' } as any}
                $withBorder={withBorder}
                $showPetOnlyStyle={showPetOnlyStyle}>
                {name}
              </StyledBubbleText>
              {topIconName && (
                <StyledAffinityTagWrapper
                  style={
                    {
                      '--bgColor': textColor,
                      '--iconX': iconConfig.x + 'px',
                      '--iconY': iconConfig.y + 'px',
                      '--iconSize': iconConfig.size || 30 + 'px',
                    } as any
                  }>
                  <StyledAffinityTag>
                    <SpriteSvg id={topIconName} />
                  </StyledAffinityTag>
                </StyledAffinityTagWrapper>
              )}
            </div>
          </UIBox>
        </Html>
      )}
    </group>
  );
}
