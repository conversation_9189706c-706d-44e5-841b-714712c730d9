import { Html } from '@react-three/drei';
import React from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';
import MarkdownRender from '@rootComponents/MarkdownRender';

import styled from 'styled-components';

const UIBox = styled.div`
  .bubble-container {
    position: relative;
    display: inline-block;
    bottom: 0;
    pointer-events: none; /* 使鼠标事件穿透 */
  }

  //

  .bubble-container-bg {
    left: -60px;
    top: -50px;
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    border-top: 50px solid transparent; /* 为九宫格留出边框部分 */
    border-left: 60px solid transparent; /* 为九宫格留出边框部分 */
    border-bottom: 70px solid transparent; /* 为九宫格留出边框部分 */
    border-right: 60px solid transparent; /* 为九宫格留出边框部分 */
    border-image: url('/image/topChat/chat_bg.webp'); /* 九宫格拉伸 */
    border-image-slice: 125 150 175 150 fill;
    pointer-events: none; /* 使鼠标事件穿透 */
    border-image-repeat: no-repeat;
    transform: translateZ(0); /* 强制硬件加速 */
    backface-visibility: hidden; /* 防止渲染问题 */
  }

  .image-slider {
    position: relative;
    padding: 10px 80px 10px 80px;

    .image-slider-img {
      width: 150px; /* 根据图片大小调整 */
      height: 30px; /* 根据图片大小调整 */
      background-size: 100% 100%;
    }
  }

  .bubble-text {
    position: relative; /* 改为相对定位，方便独立展示 */
    max-width: 900px; /* 固定宽度 */
    color: #140f08;
    font-size: 35px;
    word-wrap: break-word; /* 允许长单词换行 */
    white-space: normal; /* 多行文字正常换行 */
    user-select: none;
    pointer-events: none; /* 使鼠标事件穿透 */
  }
`;

export default function TopChatUI({ height, word }: { height: number; word: string }) {
  const ref = React.useRef<THREE.Group>(null);
  const sliderRef = React.useRef<HTMLImageElement>(null);
  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      const cameraWorldPos = new THREE.Vector3();
      camera.getWorldPosition(cameraWorldPos);
      const objWorldPos = new THREE.Vector3();
      ref.current.getWorldPosition(objWorldPos);
      cameraWorldPos.y = objWorldPos.y;
      ref.current.lookAt(cameraWorldPos);
    }
    if (sliderRef.current) {
      //秒
      const second = Math.floor((Date.now() * 3) / 1000);
      //0.5秒替换一次index
      const index = (second % 3) + 1;
      const divList = sliderRef.current.getElementsByTagName('div');
      for (let i = 0; i < divList.length; i++) {
        divList[i].style.display = index == i + 1 ? 'block' : 'none';
      }
    }
  });
  return (
    <group ref={ref} position={[0, height, 0]}>
      {word.length > 0 && (
        <Html
          distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
          transform // 保证HTML内容的大小不随距离变化
          // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
          pointerEvents="none" // 禁用鼠标事件
          position={[0, 0, 0]}
          style={{
            transformOrigin: 'bottom center',
            transform: 'translate(0,-50%)',
            transition: 'all 0.3s ease',
            left: '50%',
          }}
          center={false}
        >
          <UIBox>
            <div className="bubble-container">
              <div className="bubble-container-bg" />
              {word !== '...' && (
                <div className="bubble-text">
                  <MarkdownRender context={word} />
                </div>
              )}
              {word === '...' && (
                <div className="image-slider">
                  <div className="image-slider-img" ref={sliderRef}>
                    <div
                      className="image-slider-img"
                      style={{ backgroundImage: 'url(/image/topChat/chat1.webp)' }}
                    />
                    <div
                      className="image-slider-img"
                      style={{ backgroundImage: 'url(/image/topChat/chat2.webp)' }}
                    />
                    <div
                      className="image-slider-img"
                      style={{ backgroundImage: 'url(/image/topChat/chat3.webp)' }}
                    />
                  </div>
                </div>
              )}
            </div>
          </UIBox>
        </Html>
      )}
    </group>
  );
}
