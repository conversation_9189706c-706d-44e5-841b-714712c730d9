import React, { useEffect, useMemo, useRef, useState } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { Camera } from 'three';
import { Raycaster } from 'three/src/Three.Core';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import { IAppState } from '@/constant/type';
import GlobalSpace from '@/game/Global/GlobalSpace';
import CustomCameraController from '@/game/TSX/Util/FreeCameraController';
import { KeyboardControls } from '@react-three/drei';
import Ecctrl, { CustomEcctrlRigidBody } from '@/game_lib/Ecctrl';
import { getCirclePoints } from '@/utils';
import { TreeData } from '@/game/Config/TreeConfig';
import { ConfigManager } from '@/game/Config/ConfigManager';
import { ItemData } from '@/game/Config/ItemConfig';
import createUseGame, { UseGameState } from '@/game_lib/stores/useGame';
import { HandItemDetail } from '@/game/TSX/HandItem';
import AudioSystemComponent, { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import { StoneData } from '@/game/Config/StoneConfig';
import { FishingAreaData } from '@/game/Config/FishingAreaConfig';
import { FishConfig, FishData, FishFloatObject, FishStatus } from '@/game/Config/FishConfig';
import { DecryptedDataJson } from '@/hooks/useFish';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import { AvatarActionConfig, AvatarActionData } from '@/game/Config/AvatarActionConfig';
import { usePlayerEnergySelector } from '@/contexts/playerEnergyContext';
import PetModelList from '@/game/TSX/Pet/PetModelList';
import CreateEntity from '@/game/TSX/MyPlayerMesh';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';
import PetModel from '@/game/TSX/Pet/PetModel';

export enum AppGameApiKey {
  authTwitter = 'authTwitter',
  receiveAxe = 'receiveAxe',
  receiveTool = 'receiveTool',
  receivePickaxe = 'receivePickaxe',
  receiveFishingRod = 'receiveFishingRod',
  useFishingRod = 'useFishingRod',
  fishingSuccess = 'fishingSuccess',
  finishFishEgg = 'finishFishEgg',
  cutTree = 'cutTree',
  mining = 'mining',
  startDogEgg = 'startDogEgg',
  activityRule = 'activityRule',
  showCombo = 'showCombo',
  showRewards = 'showRewards',
  setLoaderType = 'setLoaderType',
  openSynthesis = 'openSynthesis',
  disconnectWallet = 'disconnectWallet',
  sendChat = 'sendChat',
  // buyTools = 'buyTools',
  buyEnergy = 'buyEnergy',
  submitResources = 'submitResources',
  shareTweets = 'shareTweets',
  donateTokens = 'donateTokens',
  claimDrop = 'claimDrop',
  updateCommunityRank = 'updateCommunityRank',
  updateFishRank = 'updateFishRank',
  updateTreeRank = 'updateTreeRank',
  updateStoneRank = 'updateStoneRank',
  refreshRank = 'refreshRank',
  pickUpDrop = 'pickUpDrop',
  updateItemDurability = 'updateItemDurability',
  createParticle = 'createParticle',
  getDispatch = 'getDispatch',
  setMetaData = 'setMetaData',

  showEasterEggRewards = 'showEasterEggRewards',
  showEasterEggFailed = 'showEasterEggFailed',
  setOrderTreeTimeLeft = 'setOrderTreeTimeLeft',
  nextDay = 'nextDay',

  web3PetOrderQuery = 'web3PetOrderQuery',
  petCutTree = 'petCutTree',
  petMining = 'petMining',
  petFish = 'petFish',
  petIdleSlowRecovery = 'petIdleSlowRecovery',

  updatePetShedInfo = 'updatePetShedInfo',
  petUpdateMaterial = 'petUpdateMaterial',

  removeItemId = 'removeItemId',
}

function ShortcutControl() {
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const [actionMap, setActionMap] = useState<Map<string, AvatarActionData>>(new Map());

  useEffect(() => {
    const keyList = Array.from(actionMap.keys());
    const cancel = KeyPressUtil.registerKeyPress(keyList, (event) => {
      const action = actionMap.get(event.key);
      if (action) {
        setCurAnimation(action.name);
      }
    });
    return () => {
      cancel();
    };
  }, [actionMap]);

  useEffect(() => {
    AvatarActionConfig.getInstance().getDataList((dataList: AvatarActionData[]) => {
      const map = new Map<string, AvatarActionData>();
      dataList.forEach((data: AvatarActionData) => {
        if (data.shortcut.length > 0) {
          map.set(data.shortcut, data);
        }
      });
      setActionMap(map);
    });
  }, []);

  return <></>;
}

class MyPlayerClass {
  clientEntityIndex = 0;
  position: THREE.Vector3;
  quaternion: THREE.Quaternion;
  targetDirection: THREE.Vector3 | null = null;
  itemData: ItemData | null = null;
  btcAddress = '';
  sessionId = '';
  UsePetInscriptionId = '';
  pickedList: string[] = [];

  dogEasterEgg: IAppState['dogEasterEgg'] | null = null;
  whackAMoleEasterEgg: IAppState['whackAMoleEasterEgg'] | null = null;
  showEasterEggButton = false;

  userBasicInfo: IAppState['userBasicInfo'] | null = null;
  axeParams: IAppState['axeParams'] | null = null;

  scene: THREE.Scene | null = null;
  camera: Camera | null = null;

  refreshTimeStamp = 0;
  rigidBody: CustomEcctrlRigidBody | null = null;

  hitTreeFinishCallback: (() => void) | null = null;
  hitStoneFinishCallback: (() => void) | null = null;
  pizzaTick = '';
  pizzaCount = -1;
  private pizzaChangeCallback: ((count: number, tick: string) => void) | null = null;
  private hitTreeTimer: NodeJS.Timeout | null = null;
  private lastTreeData: TreeData | null = null;
  private hitTreeCallback: ((damage: number) => void) | null = null;
  private hitTreeStartTime = 0;
  private hitTreeLastTime = 0;
  private lastStoneData: StoneData | null = null;
  private hitStoneCallback: ((damage: number) => void) | null = null;
  private hitStoneStartTime = 0;
  private hitStoneLastTime = 0;
  private hitStoneTimer: NodeJS.Timeout | null = null;
  private fishingTimers: NodeJS.Timeout[] = [];
  private fishData: FishData | null = null;
  private fishFloatObject: FishFloatObject | null = null;
  private isFishingSuccessTime = false;
  private appApiMap: Map<AppGameApiKey, any> = new Map();
  private serverItemIdMap: Map<string, boolean> = new Map();
  destroyItemIdMap: Map<string, boolean> = new Map();

  private mapPointMap: Map<
    string,
    {
      pos: THREE.Vector3;
      callback: (distance: number) => void;
    }
  > = new Map();

  constructor() {
    this.position = new THREE.Vector3(0, 0, 0);
    this.quaternion = new THREE.Quaternion();
  }

  getUseGame() {
    return GlobalSpace.getMyUseGame();
  }

  saveServerItemId(serverId: string) {
    this.serverItemIdMap.set(serverId, true);
  }

  isNewServerItemId(serverId: string) {
    return !this.serverItemIdMap.has(serverId);
  }

  destroyHandItem() {
    if (this.axeParams) {
      if (this.destroyItemIdMap.has(this.axeParams.userItemId || '')) {
        return;
      }
      this.destroyItemIdMap.set(this.axeParams.userItemId || '', true);
      this.callAppApi(AppGameApiKey.removeItemId, this.axeParams.userItemId);
    }
  }

  registerMapPoint(key: string, pos: THREE.Vector3, callback: (distance: number) => void) {
    this.mapPointMap.set(key, { pos, callback });
  }

  unregisterMapPoint(key: string) {
    this.mapPointMap.delete(key);
  }

  checkAllMapPoint() {
    const list = Array.from(this.mapPointMap.values());
    const playerPos = new THREE.Vector3(this.position.x, 0, this.position.z);
    list.forEach((item) => {
      item.pos.y = 0;
      const distance = playerPos.distanceTo(item.pos);
      item.callback(distance);
    });
  }

  setAppApi(key: AppGameApiKey, func: any) {
    this.appApiMap.set(key, func);
  }

  callAppApi(key: AppGameApiKey, ...args: any[]) {
    const func = this.appApiMap.get(key);
    if (func) {
      func(...args);
    }
  }

  moveToPoint(point: THREE.Vector3, callback: () => void) {
    if (this.rigidBody && this.rigidBody.moveCharacterToPoint) {
      this.rigidBody.moveCharacterToPoint(point, () => {
        callback();
      });
    }
  }

  changeHandItem(itemData: ItemData | null) {
    this.itemData = itemData;
  }

  startPizza(tick: string) {
    this.pizzaTick = tick;
    this.pizzaCount = 0;
    this.pizzaChangeCallback && this.pizzaChangeCallback(this.pizzaCount, this.pizzaTick);
  }

  addPizza() {
    this.pizzaCount++;
    AudioSystem.playAudio('myPlayer', './sound/reward/pick_up.wav', () => {
      return true;
    });
    this.pizzaChangeCallback && this.pizzaChangeCallback(this.pizzaCount, this.pizzaTick);
  }

  removePizza() {
    this.pizzaCount = -1;
    this.pizzaTick = '';
    this.pizzaChangeCallback && this.pizzaChangeCallback(this.pizzaCount, this.pizzaTick);
  }

  watchPizzaChange(callback: (count: number, tick: string) => void) {
    this.pizzaChangeCallback = callback;
  }

  facePosition(targetPosition: THREE.Vector3) {
    targetPosition.y = this.position.y;
    this.targetDirection = this.position.clone().sub(targetPosition).normalize();
    if (this.rigidBody && this.rigidBody.rotateCharacterOnY) {
      const rotY = Math.atan2(-this.targetDirection.x, -this.targetDirection.z);
      this.rigidBody.rotateCharacterOnY(rotY, true);
    }
  }

  faceCamera() {
    if (this.camera) {
      this.facePosition(this.camera.position.clone());
    }
  }

  hitTree(
    callback: (isCombo: boolean, damage: number) => void,
    hitCallback: (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => void,
    finishCallback: () => void,
    failCallback: () => void,
    scene: THREE.Scene,
    treeData: TreeData
  ) {
    if (this.lastTreeData && this.lastTreeData !== treeData) {
      //防止同时砍两棵树导致的异常
      return;
    }

    const hitStart = () => {
      this.lastTreeData = treeData;
      const debugItemWidth = Number(localStorage.getItem('debugItemWidth'));
      const itemWidth = debugItemWidth != 0 ? debugItemWidth : this.itemData?.axe_width || 0;
      const debugItemLength = Number(localStorage.getItem('debugItemLength'));
      const itemLength = debugItemLength != 0 ? debugItemLength : this.itemData?.axe_length || 0;
      ConfigManager.getInstance().getData((data) => {
        const facePoint = getCirclePoints(
          treeData.position[0],
          treeData.position[2],
          treeData.radius + itemWidth,
          this.position.x,
          this.position.z,
          itemLength
        );
        const treePos = new THREE.Vector3(
          treeData.position[0],
          treeData.position[1] + 0.5,
          treeData.position[2]
        );
        const facePos = new THREE.Vector3(facePoint.x, treeData.position[1] + 0.5, facePoint.y);
        const hitPos = treePos.clone();
        hitPos.add(facePos.clone().sub(treePos).normalize().multiplyScalar(treeData.radius));
        // hitPos.y = treePos.y
        const success = (isCombo: boolean) => {
          this.hitTreeCallback = (damage: number) => {
            const object = new THREE.Object3D();
            object.position.copy(treePos);
            object.lookAt(facePos);
            hitCallback(damage, hitPos, object.quaternion);
          };
          this.hitTreeFinishCallback = () => {
            this.lastTreeData = null;
            this.hitTreeStartTime = 0;
            this.hitTreeLastTime = 0;
            finishCallback();
            this.hitTreeFinishCallback = null;
            this.hitTreeCallback = null;
          };

          this.hitTreeLastTime = now;
          const damage = this.itemData?.damage || 0;
          callback(isCombo, damage);
        };

        const now = Date.now();
        if (this.hitTreeStartTime == 0) {
          this.hitTreeStartTime = now;
          success(false);
        } else if (this.hitTreeStartTime > 0) {
          const hitTime = now - this.hitTreeLastTime;
          const isFirst = this.hitTreeStartTime == this.hitTreeLastTime;
          if (isFirst) {
            if (
              hitTime > data.tree_first_combo_range[0] &&
              hitTime < data.tree_first_combo_range[1]
            ) {
              success(true);
            } else {
              failCallback();
            }
          } else {
            if (
              hitTime > data.tree_second_combo_range[0] &&
              hitTime < data.tree_second_combo_range[1]
            ) {
              success(true);
            } else {
              failCallback();
            }
          }
        }

        const debugHitTree = localStorage.getItem('debugHitTree') === 'true';
        if (debugHitTree) {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({ color: 0xff0000 }); // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.copy(hitPos);
          scene.add(sphere);
          const material1 = new THREE.MeshBasicMaterial({ color: 0x0000ff }); // 黄色高亮
          const sphere1 = new THREE.Mesh(geometry, material1);
          sphere1.position.copy(facePos);
          scene.add(sphere1);

          setTimeout(() => {
            scene.remove(sphere);
            scene.remove(sphere1);
          }, 2000);
        }
        this.facePosition(facePos);
      });
    };

    const direction = new THREE.Vector3(
      this.position.x - treeData.position[0],
      0,
      this.position.z - treeData.position[2]
    );
    const distance = direction.length();
    if (distance > treeData.range[1] && distance < treeData.range[2]) {
      hitStart();
    } else {
      const targetDistance = treeData.range[2] < distance ? treeData.range[1] : treeData.range[2];
      const targetPos = direction
        .normalize()
        .multiplyScalar(targetDistance)
        .add(new THREE.Vector3(treeData.position[0], treeData.position[1], treeData.position[2]));

      if (this.hitTreeTimer) {
        clearTimeout(this.hitTreeTimer);
        this.hitTreeTimer = null;
      }
      this.moveToPoint(targetPos, () => {
        this.hitTreeTimer = setTimeout(() => {
          hitStart();
        }, 100);
      });
    }
  }

  hitStone(
    callback: (isCombo: boolean, damage: number) => void,
    hitCallback: (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => void,
    finishCallback: () => void,
    failCallback: () => void,
    scene: THREE.Scene,
    stoneData: StoneData
  ) {
    if (this.lastStoneData && this.lastStoneData !== stoneData) {
      //防止同时砍两棵树导致的异常
      return;
    }

    const hitStart = () => {
      this.lastStoneData = stoneData;
      ConfigManager.getInstance().getData((data) => {
        const stonePos = new THREE.Vector3(
          stoneData.position[0],
          stoneData.position[1] + 0.5,
          stoneData.position[2]
        );
        const facePos = stonePos.clone();
        const object = new THREE.Object3D();
        object.position.copy(this.position);
        object.lookAt(stonePos);
        const object2 = new THREE.Object3D();
        object2.position.set(0, 0.18, 1.5);
        object.add(object2);
        const hitPos = new THREE.Vector3(0, 0, 0);
        object2.getWorldPosition(hitPos);
        const success = (isCombo: boolean) => {
          this.hitStoneCallback = (damage: number) => {
            const object = new THREE.Object3D();
            object.position.copy(stonePos);
            object.lookAt(hitPos);
            hitCallback(damage, hitPos, object.quaternion);
          };
          this.hitStoneFinishCallback = () => {
            this.lastStoneData = null;
            this.hitStoneStartTime = 0;
            this.hitStoneLastTime = 0;
            finishCallback();
            this.hitStoneFinishCallback = null;
            this.hitStoneCallback = null;
          };

          this.hitStoneLastTime = now;
          const damage = this.itemData?.damage || 0;
          callback(isCombo, damage);
        };

        const now = Date.now();
        if (this.hitStoneStartTime == 0) {
          this.hitStoneStartTime = now;
          success(false);
        } else if (this.hitStoneStartTime > 0) {
          const hitTime = now - this.hitStoneLastTime;
          const isFirst = this.hitStoneStartTime == this.hitStoneLastTime;
          if (isFirst) {
            if (
              hitTime > data.stone_first_combo_range[0] &&
              hitTime < data.stone_first_combo_range[1]
            ) {
              success(true);
            } else {
              failCallback();
            }
          } else {
            if (
              hitTime > data.stone_second_combo_range[0] &&
              hitTime < data.stone_second_combo_range[1]
            ) {
              success(true);
            } else {
              failCallback();
            }
          }
        }
        const debugHitTree = localStorage.getItem('debugHitTree') === 'true';
        if (debugHitTree) {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({ color: 0xff0000 }); // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.copy(hitPos);
          scene.add(sphere);

          setTimeout(() => {
            scene.remove(sphere);
          }, 2000);
        }
        this.facePosition(facePos);
      });
    };

    const direction = new THREE.Vector3(
      this.position.x - stoneData.position[0],
      0,
      this.position.z - stoneData.position[2]
    );
    const distance = direction.length();
    if (distance > stoneData.range[1] && distance < stoneData.range[2]) {
      hitStart();
    } else {
      const targetDistance =
        stoneData.range[2] < distance ? stoneData.range[1] : stoneData.range[2];
      const targetPos = direction
        .normalize()
        .multiplyScalar(targetDistance)
        .add(
          new THREE.Vector3(stoneData.position[0], stoneData.position[1], stoneData.position[2])
        );

      if (this.hitStoneTimer) {
        clearTimeout(this.hitStoneTimer);
        this.hitStoneTimer = null;
      }
      this.moveToPoint(targetPos, () => {
        this.hitStoneTimer = setTimeout(() => {
          hitStart();
        }, 100);
      });
    }
  }

  startFishing(
    fishCallback: (fishPos: THREE.Vector3) => void,
    callback: (fishData: FishData) => void,
    finishCallback: () => void,
    areaData: FishingAreaData
  ) {
    if (this.fishingTimers.length > 0) {
      return;
    }

    this.fishingTimers.push(
      setTimeout(() => {
        if (this.rigidBody && this.rigidBody.rotateCharacterOnY) {
          //-90 为了和npc的对齐
          this.rigidBody.rotateCharacterOnY((areaData.fishing_yaw / 180) * Math.PI, true);
          const object = new THREE.Object3D();
          object.position.copy(this.position);
          object.quaternion.setFromEuler(
            new THREE.Euler(0, (areaData.fishing_yaw / 180) * Math.PI, 0)
          );
          const fishObject = new THREE.Object3D();
          fishObject.position.set(-0.5, 0.5, 2.5);
          object.add(fishObject);
          const fishPos = fishObject.getWorldPosition(new THREE.Vector3());
          // 创建Raycaster实例
          const raycaster = new Raycaster();
          // 从中心点到生成的点发射射线
          const direction = new THREE.Vector3(0, -1, 0);
          raycaster.set(fishPos, direction);
          raycaster.far = 8; //多检测1的长度 , 给出生点留出体积
          const waterMesh = FishConfig.getInstance().getWaterMesh();
          if (waterMesh) {
            // 获取射线与场景中物体的相交情况
            const intersects = raycaster.intersectObjects([waterMesh]);
            if (intersects.length > 0) {
              const intersect = intersects[0];
              fishCallback(intersect.point);
            }
          } else {
            fishCallback(fishPos);
          }

          this.callAppApi(AppGameApiKey.useFishingRod, (json: DecryptedDataJson) => {
            if (json && json.tag) {
              const randomFishId = Number(json.tag);

              FishConfig.getInstance().getData(randomFishId, (fishData) => {
                this.fishData = fishData;
                const fishFloatObject = FishConfig.getInstance().getObject(randomFishId);
                this.fishFloatObject = fishFloatObject;
                callback(fishData);
                const timeObject = FishConfig.getInstance().randomFishTimeObject(fishData);
                fishFloatObject.status = FishStatus.Hide;
                this.fishingTimers.push(
                  setTimeout(() => {
                    fishFloatObject.status = FishStatus.Show;
                  }, timeObject.showTime)
                );
                this.fishingTimers.push(
                  setTimeout(() => {
                    fishFloatObject.status = FishStatus.Show;
                  }, timeObject.showTime + 3000)
                );
                timeObject.biteTimeList.forEach((item) => {
                  this.fishingTimers.push(
                    setTimeout(() => {
                      fishFloatObject.status = FishStatus.Bite;
                    }, item.biteTime)
                  );
                  this.fishingTimers.push(
                    setTimeout(() => {
                      this.isFishingSuccessTime = true;
                    }, item.successStartTime)
                  );
                  this.fishingTimers.push(
                    setTimeout(() => {
                      this.isFishingSuccessTime = false;
                    }, item.successEndTime)
                  );
                  this.fishingTimers.push(
                    setTimeout(() => {
                      fishFloatObject.status = FishStatus.Idle;
                    }, item.biteEndTime)
                  );
                });

                this.fishingTimers.push(
                  setTimeout(() => {
                    this.fishingTimers.forEach((timer) => {
                      clearTimeout(timer);
                    });
                    this.fishingTimers = [];
                    finishCallback();
                    this.fishData = null;
                  }, timeObject.totalTime)
                );
              });
            } else {
              this.fishingTimers.forEach((timer) => {
                clearTimeout(timer);
              });
              this.fishingTimers = [];
              finishCallback();
              this.fishData = null;
            }
          });
        }
      }, 100)
    );
  }

  stopFishing(callback: (success: boolean, fishData: FishData | null) => void) {
    this.fishingTimers.forEach((timer) => {
      clearTimeout(timer);
    });
    this.fishingTimers = [];
    callback(this.isFishingSuccessTime, this.fishData);
    if (this.fishFloatObject) {
      this.fishFloatObject.status = this.isFishingSuccessTime ? FishStatus.Catch : FishStatus.Idle;
      this.fishFloatObject = null;
    }
    this.isFishingSuccessTime = false;
    this.fishData = null;
  }

  onUpdate() {
    const now = Date.now();
    if (this.hitTreeStartTime > 0) {
      ConfigManager.getInstance().getData((data) => {
        if (this.hitTreeFinishCallback) {
          const hitTime = now - this.hitTreeLastTime;
          const firstHit = this.hitTreeLastTime == this.hitTreeStartTime;
          if (firstHit) {
            if (hitTime > data.tree_first_combo_range[0]) {
              this.hitTreeCallback && this.hitTreeCallback(this.itemData?.damage || 0);
              this.hitTreeCallback = null;
            }
            if (hitTime > data.tree_first_combo_range[2]) {
              this.hitTreeFinishCallback();
            }
          } else {
            if (hitTime > data.tree_second_combo_range[0]) {
              this.hitTreeCallback && this.hitTreeCallback(this.itemData?.damage || 0);
              this.hitTreeCallback = null;
            }
            if (hitTime > data.tree_second_combo_range[2]) {
              this.hitTreeFinishCallback();
            }
          }
        }
      });
    }
    if (this.hitStoneStartTime > 0) {
      ConfigManager.getInstance().getData((data) => {
        if (this.hitStoneFinishCallback) {
          const hitTime = now - this.hitStoneLastTime;
          const firstHit = this.hitStoneLastTime == this.hitStoneStartTime;
          if (firstHit) {
            if (hitTime > data.stone_first_combo_range[0]) {
              this.hitStoneCallback && this.hitStoneCallback(this.itemData?.damage || 0);
              this.hitStoneCallback = null;
            }
            if (hitTime > data.stone_first_combo_range[2]) {
              this.hitStoneFinishCallback();
            }
          } else {
            if (hitTime > data.stone_second_combo_range[0]) {
              this.hitStoneCallback && this.hitStoneCallback(this.itemData?.damage || 0);
              this.hitStoneCallback = null;
            }
            if (hitTime > data.stone_second_combo_range[2]) {
              this.hitStoneFinishCallback();
            }
          }
        }
      });
    }
  }
}

export function MyPlayerElement({ myPlayer }: { myPlayer: MyPlayerClass }) {
  const { scene, camera } = useThree();
  const initPos = new THREE.Vector3(
    myPlayer.position.x,
    myPlayer.position.y + 1,
    myPlayer.position.z
  );
  const useGame = myPlayer.getUseGame();
  const setWeary = useGame((state: UseGameState) => state.setWeary);
  const [myAvatarData, setMyAvatarData] = useState<IAvatarMetadata | null>(null);
  const [petId, setPetId] = useState<string>('');
  const [usePetInscriptionId, setUsePetInscriptionId] = useState<string>('');
  const [freeCamera, setFreeCamera] = useState<boolean>(false);
  const [stopControl, setStopControl] = useState<boolean>(false);
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);
  const [keyboardMap, setKeyboardMap] = useState<{ name: string; keys: string[] }[]>([]);

  const freeTime = usePlayerEnergySelector((state) => state.freeTime);

  const itemDetail = useMemo(() => {
    return {
      isMe: true,
      itemId: Number(myPlayer.axeParams?.tag || 0),
      curDurability: Number(myPlayer.axeParams?.currentDurability || 0),
      showEffect: false,
      serverId: myPlayer.axeParams?.userItemId || '',
      useGame: createUseGame(),
    } as HandItemDetail;
  }, []);
  const playerRef = useRef<CustomEcctrlRigidBody>(null);
  useEffect(() => {
    KeyPressUtil.setCallback((enable) => {
      setStopControl(!enable);
    });
    const usePetInscriptionIdKey = GlobalSpaceEvent.ListenKeyDataChange<string>(
      GlobalDataKey.UsePetInscriptionId,
      (value) => {
        myPlayer.UsePetInscriptionId = value;
        setUsePetInscriptionId(value);
      }
    );
    const myAvatarDataKey = GlobalSpaceEvent.ListenKeyDataChange<IAvatarMetadata>(
      GlobalDataKey.MyAvatarData,
      (value) => {
        setMyAvatarData(null);
        setTimeout(() => {
          setMyAvatarData(value);
        }, 1);
      }
    );

    const openFreeCameraKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.OpenFreeCamera,
      (value) => {
        setFreeCamera(value);
      }
    );

    const sceneLoadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
        if (value) {
          setStopControl(true);
        } else {
          setStopControl(!KeyPressUtil.getEnable());
        }
      }
    );
    myPlayer.scene = scene;
    myPlayer.camera = camera;

    return () => {
      myPlayer.scene = null;
      myPlayer.camera = null;
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.UsePetInscriptionId, usePetInscriptionIdKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.UsePetInscriptionId, myAvatarDataKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.OpenFreeCamera, openFreeCameraKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, sceneLoadingKey);
    };
  }, []);

  useEffect(() => {
    if (stopControl || freeCamera) {
      setKeyboardMap([]);
    } else {
      setKeyboardMap([
        { name: 'forward', keys: ['ArrowUp', 'KeyW'] },
        { name: 'backward', keys: ['ArrowDown', 'KeyS'] },
        { name: 'leftward', keys: ['ArrowLeft', 'KeyA'] },
        { name: 'rightward', keys: ['ArrowRight', 'KeyD'] },
        { name: 'jump', keys: ['Space'] },
        { name: 'walk', keys: ['Shift'] },
      ]);
    }
  }, [stopControl, freeCamera]);

  useEffect(() => {
    const now = Date.now();
    const isWeary = freeTime > 0 && now < freeTime;
    setWeary(isWeary);
    if (isWeary) {
      const timer = setTimeout(() => {
        setWeary(false);
      }, freeTime - now);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [setWeary, freeTime]);

  useEffect(() => {
    myAvatarData && setPetId(myAvatarData.petId || '');
  }, [myAvatarData]);

  useFrame(({ camera }) => {
    myPlayer.rigidBody = playerRef.current;
    myPlayer.onUpdate();

    if (myPlayer.btcAddress) {
      const currentDurability = Number(myPlayer.axeParams?.currentDurability || 0);
      const itemId = Number(myPlayer.axeParams?.tag || 0);
      const serverId = myPlayer.axeParams?.userItemId || '';
      if (myPlayer.destroyItemIdMap.has(serverId)) {
        itemDetail.itemId = 0;
        itemDetail.curDurability = 0;
        itemDetail.serverId = '';
        itemDetail.showEffect = false;
      } else {
        itemDetail.itemId = itemId;
        itemDetail.serverId = serverId;
        itemDetail.curDurability = currentDurability;
        itemDetail.showEffect = myPlayer.dogEasterEgg !== null;
      }
    } else {
      itemDetail.itemId = 0;
      itemDetail.curDurability = 0;
      itemDetail.serverId = '';
      itemDetail.showEffect = false;
    }
  });

  return (
    <>
      {petId.length > 0 && (
        <PetModel usePet={'/assets/Pet/Pet_01.glb'} follow={keyboardMap.length > 0} />
      )}
      <PetModelList />
      {freeCamera && <CustomCameraController />}
      <KeyboardControls map={keyboardMap}>
        <Ecctrl
          useGame={useGame}
          useJoystick={true}
          followLight
          mode="PointToMove"
          debug={false}
          disableFollowCam={freeCamera}
          animated={true}
          position={[initPos.x, initPos.y, initPos.z]}
          camInitDir={{ x: 0.3, y: 0 }}
          jumpVel={3.7}
          sprintJumpMult={1.05}
          camFollowMult={9}
          turnSpeed={11}
          sprintWalkMult={0.7}
          ref={playerRef}>
          <ShortcutControl />
          {myAvatarData && <CreateEntity avatarData={myAvatarData} itemDetail={itemDetail} />}
          <AudioSystemComponent _key={'myPlayer'} />
        </Ecctrl>
      </KeyboardControls>
    </>
  );
}

let MyPlayer: MyPlayerClass | null = null;

export function GetMyPlayer() {
  if (MyPlayer == null) {
    MyPlayer = new MyPlayerClass();
  }
  return MyPlayer;
}

/**
 * @description 获取单例的MyPlayer实例
 * @returns {MyPlayerClass} 返回MyPlayer实例或null
 */
export function useMyPlayer(): MyPlayerClass {
  const playerRef = useRef<MyPlayerClass | null>(null);

  if (!playerRef.current && MyPlayer) {
    playerRef.current = MyPlayer;
  }

  useEffect(() => {
    if (!MyPlayer) {
      MyPlayer = new MyPlayerClass();
    }
    playerRef.current = MyPlayer;
  }, []);

  return playerRef.current as MyPlayerClass;
}
