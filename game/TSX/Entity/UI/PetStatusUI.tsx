import { EntityPetUIStatus } from '@/game/TS/Entity/Components/EntityTopUI';
import styled from 'styled-components';
import React from 'react';

const StatusUI = styled.div`
  position: relative;
  bottom: 60px;
  pointer-events: none; /* 使鼠标事件穿透 */
  width: 160px;
  height: 185px;
  background-size: 100% 100%;
`;

export function PetStatusUI({ status }: { status: EntityPetUIStatus }) {
  if (status === EntityPetUIStatus.Spa) {
    return (
      <StatusUI
        style={{
          backgroundImage: 'url(/image/pet/pet_status_recovery.svg)',
        }}
      />
    );
  }
  return <div></div>;
}
