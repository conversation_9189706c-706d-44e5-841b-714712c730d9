import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { POPOVER_HEIGHT } from '@/constant';
import { EntityTopUI, EntityTopUIType } from '@/game/TS/Entity/Components/EntityTopUI';
import { Html } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { PetStatusUI } from '@/game/TSX/Entity/UI/PetStatusUI';

type TopUIProps = {
  type: EntityTopUIType;
  params: string[];
};

function TopUIRoot({ props }: { props: TopUIProps[] }) {
  return (
    <Html
      distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
      transform // 保证HTML内容的大小不随距离变化
      // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
      pointerEvents="none" // 禁用鼠标事件
      position={[0, 0, 0]}
      style={{
        transformOrigin: 'bottom center',
        transform: 'translate(0,-50%)',
        transition: 'all 0.3s ease',
        left: '50%',
      }}
      center={false}>
      {props.map((item) => {
        if (item.type === EntityTopUIType.PetStatus) {
          return <PetStatusUI key={item.type} status={Number(item.params[0])} />;
        }
        return null;
      })}
    </Html>
  );
}

export default function EntityTopUITSX({
  topUI,
  root,
}: {
  topUI: EntityTopUI;
  root: THREE.Object3D;
}) {
  const ref = React.useRef<THREE.Group>(null);
  const [height, setHeight] = useState<number>(0);
  const [props, setProps] = useState<TopUIProps[]>([]);
  const cameraWorldPosRef = useRef<THREE.Vector3>(new THREE.Vector3());
  const objWorldPosRef = useRef<THREE.Vector3>(new THREE.Vector3());
  useEffect(() => {
    const update = () => {
      const props: TopUIProps[] = [];
      topUI.uiTypeMap.forEach((value, key) => {
        props.push({
          type: key,
          params: value,
        });
      });
      setProps(props);
    };
    update();
    topUI.registerUpdateCallback(update);
    return () => {
      topUI.registerUpdateCallback(() => undefined);
    };
  }, [topUI]);

  useEffect(() => {
    const box = new THREE.Box3().setFromObject(root);
    // box 的 min 和 max 属性代表边界框的最小和最大坐标
    setHeight(Math.max(box.max.y - box.min.y + POPOVER_HEIGHT, 0));
  }, [root]);

  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      camera.getWorldPosition(cameraWorldPosRef.current);
      ref.current.getWorldPosition(objWorldPosRef.current);
      cameraWorldPosRef.current.y = objWorldPosRef.current.y;
      ref.current.lookAt(cameraWorldPosRef.current);
    }
  });
  return (
    <>
      {props.length > 0 && (
        <group ref={ref} position={[0, height, 0]}>
          <TopUIRoot props={props} />
        </group>
      )}
    </>
  );
}
