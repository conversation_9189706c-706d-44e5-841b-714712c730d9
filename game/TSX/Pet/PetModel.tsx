/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import createUseGame, { UseGameState } from '@/game_lib/stores/useGame';
import Ecctrl from '@/game_lib/Ecctrl';

function CreateMesh({ useGame, usePet }: { usePet: string; useGame: any }) {
  const groupRef = useRef<THREE.Group>(null);
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);
  const worldPosRef = useRef(new THREE.Vector3());
  const worldQuaternionRef = useRef(new THREE.Quaternion());
  const [petEntity, setPetEntity] = useState<Entity | null>(null);

  useEffect(() => {
    initializeAnimationSet({
      idle: 'idle',
      wearyIdle: 'idle',
      walk: 'run',
      wearyWalk: 'run',
      run: 'run',
      jump: 'jump_01',
      jumpIdle: 'jump_02',
      jumpLand: 'jump_03',
      fall: 'fall',
    });
  }, [initializeAnimationSet]);

  useEffect(() => {
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.GlbMesh,
    ]);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.setDefaultAnimation('Action_00');
    animation?.setAnimationRename('idle', 'Action_00');
    animation?.setAnimationRename('run', 'Action_01');
    animation?.setAnimationRename('walk', 'Action_03');
    animation?.setReplaceAnimations(['Action_00', 'Action_01', 'Action_03']);
    setPetEntity(entity);
    return () => {
      EntityManager.getInstance().removeEntity(entity);
    };
  }, []);

  useEffect(() => {
    if (!petEntity) return;
    const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.playAnimation(curAnimation || 'idle');
  }, [curAnimation, petEntity]);

  useEffect(() => {
    if (!petEntity) return;
    const glbMesh = petEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    glbMesh?.setGlbUrl(usePet);
  }, [usePet, petEntity]);

  useFrame(() => {
    const group = groupRef.current;
    if (!group || !petEntity) return;
    group.getWorldPosition(worldPosRef.current);
    group.getWorldQuaternion(worldQuaternionRef.current);
    const transform = petEntity.getComponent<EntityTransform>(ComponentType.Transform);
    // console.log('worldPosRef.current', transform, worldPosRef.current);
    transform?.setPosition(worldPosRef.current, worldQuaternionRef.current);
  });
  return <group ref={groupRef} />;
}

function PetModel({ usePet, follow }: { usePet: string; follow: boolean }) {
  const groupRef = useRef<THREE.Group>(null);
  const useGame = useMemo(() => createUseGame(), []);
  const idle = useGame((state) => state.idle);
  const setCurAnimation = useGame((state) => state.setCurAnimation);
  const setMoveToPoint = useGame((state) => state.setMoveToPoint);
  const setTransformPoint = useGame((state) => state.setTransformPoint);
  const setWeary = useGame((state) => state.setWeary);
  const myPlayer = GetMyPlayer();
  const petPosRef = useRef(new THREE.Vector3());

  const getRandomOffset = () => {
    const offsetList = [
      new THREE.Vector3(1, 0.5, 0),
      new THREE.Vector3(-1, 0.5, 0),
      new THREE.Vector3(0, 0.5, 1),
      new THREE.Vector3(0, 0.5, -1),
    ];
    return offsetList[Math.floor(Math.random() * offsetList.length)];
  };

  useEffect(() => {
    setWeary(true);
  }, [setWeary]);

  const transformCd = useRef(0);
  const lockMove = useRef(false);
  useFrame(() => {
    // console.log('curPosition', curPosition, rigidRef.current);
    if (!groupRef.current) return;
    // console.log('targetPosition', targetPosition);
    const targetPosition = myPlayer.position;
    groupRef.current.getWorldPosition(petPosRef.current);
    if (!targetPosition) return;
    const distance = targetPosition.distanceTo(petPosRef.current);
    const osTime = Date.now();
    if (transformCd.current < osTime && distance > 8) {
      const randomOffset = getRandomOffset();
      randomOffset.add(targetPosition);
      transformCd.current = osTime + 3000;
      setTransformPoint(randomOffset);
      setMoveToPoint(null);
      if (idle) {
        idle();
      }
      if (setCurAnimation) {
        setCurAnimation('Action_1001');
        lockMove.current = true;
        setTimeout(() => {
          lockMove.current = false;
        }, 2000);
      }
    } else {
      if (lockMove.current) return;
      if (follow){
        setMoveToPoint(targetPosition);
      }else {
        console.log('setMoveToPoint null')
        setMoveToPoint(null);
      }
    }
  });

  return (
    <>
      {usePet.length > 0 && (
        <Ecctrl
          mode="PointToMove"
          useGame={useGame}
          animated={true}
          disableFollowCam={true}
          position={[1, 1, 0]}>
          <group position={[0, -0.92, 0]} ref={groupRef}>
            <CreateMesh useGame={useGame} usePet={usePet} />
          </group>
        </Ecctrl>
      )}
    </>
  );
}

export default PetModel;
