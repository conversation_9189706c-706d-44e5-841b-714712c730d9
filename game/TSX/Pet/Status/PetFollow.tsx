/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { Entity } from '@/game/TS/Entity/Entity';
import { AnimationType, PetFollowData } from '@/game/TSX/Pet/PetEntity';
import { useFrame } from '@react-three/fiber';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { useEffect } from 'react';
import useWalkPath from '@/game/useWalkPath';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';

function PetFollow({ petEntity, followData }: { petEntity: Entity; followData: PetFollowData }) {
  const { curPosition, followSlot, followPositions } = followData;

  const getRandomOffset = (oldPosition: THREE.Vector3) => {
    const offsetList = [
      new THREE.Vector3(0.5, 0, 0),
      new THREE.Vector3(-0.5, 0, 0),
      new THREE.Vector3(0, 0, 0.5),
      new THREE.Vector3(0, 0, -0.5),
    ];
    const offset = offsetList[Math.floor(Math.random() * offsetList.length)];
    return oldPosition.clone().add(offset);
  };

  const {
    path,
    pathIndex,
    targetPosition: oldTargetPosition,
    setTargetPosition,
  } = useWalkPath({ entity: petEntity });

  useEffect(() => {
    if (!petEntity) return;
    const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!animation) return;
    if (pathIndex < path.length) {
      animation.playAnimation(AnimationType.Walk);
    } else {
      animation.playAnimation(AnimationType.Idle);
    }
  }, [petEntity, path, pathIndex]);

  useFrame(() => {
    if (!petEntity) return;
    const walkPoint = petEntity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (!walkPoint) return;
    if (walkPoint.position) {
      curPosition.copy(walkPoint.position);
      const targetPosition = followPositions[followSlot - 1];
      if (!oldTargetPosition || targetPosition.distanceTo(oldTargetPosition) > 2) {
        setTargetPosition(getRandomOffset(targetPosition));
      }
    }
  });
  return null;
}

export default PetFollow;
