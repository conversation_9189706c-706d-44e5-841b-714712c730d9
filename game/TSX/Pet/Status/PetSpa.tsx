/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import { Entity } from '@/game/TS/Entity/Entity';
import { useEffect, useState } from 'react';
import * as THREE from 'three';
import useWalkPath from '@/game/useWalkPath';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { AnimationType } from '@/game/TSX/Pet/PetEntity';
import { useNetWork } from '@/game/TS/useNetWork';
import { useAppSelector } from '@/hooks/useStore';
import useLatest from '@/hooks/useLatest';
import { EntityPetUIStatus, EntityTopUI } from '@/game/TS/Entity/Components/EntityTopUI';

enum SpaStatus {
  Idle,
  FindPoolPoint,
  MoveToPool,
  Recovery,
}

function FindClosePool({ findSuccess }: { findSuccess: (poolPos: THREE.Vector3) => void }) {
  useEffect(() => {
    const centerPoint = new THREE.Vector3(-132.4, 1.25, -4.33);
    const totalRadius = 3.6;
    //为了平衡分布，半径的随机需要开平方
    const randomRadius = Math.sqrt(Math.random()) * totalRadius;
    const randomAngle = Math.random() * Math.PI * 2;
    const targetPoint = new THREE.Vector3(
      centerPoint.x + randomRadius * Math.cos(randomAngle),
      centerPoint.y,
      centerPoint.z + randomRadius * Math.sin(randomAngle)
    );
    findSuccess(targetPoint);
  }, []);
  return null;
}

function MoveToPool({
  entity,
  poolPos,
  moveToPoolEnd,
}: {
  entity: Entity;
  poolPos: THREE.Vector3;
  moveToPoolEnd: () => void;
}) {
  const { path, pathIndex, setTargetPosition } = useWalkPath({ entity });

  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (path.length > 0 && animation) {
      animation.playAnimation(AnimationType.Walk);
      if (pathIndex === path.length) {
        animation.playAnimation(AnimationType.Idle);
        moveToPoolEnd();
      }
    }
  }, [path, pathIndex, entity]);

  useEffect(() => {
    setTargetPosition(poolPos);
  }, [poolPos]);

  return null;
}

function Recovery({
  entity,
  serverId,
  recoverInterval,
}: {
  entity: Entity;
  serverId: string;
  recoverInterval: number;
}) {
  const { sendPetIdleSlowRecovery } = useNetWork();
  const petList = useAppSelector((state) => state.GameReducer.petList);

  const petItem = petList.find((item) => item._id === serverId);
  const latestPetItemInfo = useLatest(petItem);

  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.playAnimation(AnimationType.Idle);
    if (
      latestPetItemInfo.current &&
      latestPetItemInfo.current.stamina === latestPetItemInfo.current.currentStamina
    ) {
      return;
    }
    const topUI = entity.getComponent<EntityTopUI>(ComponentType.TopUI);
    topUI?.setPetStatus(EntityPetUIStatus.Spa);
    const interval = setInterval(() => {
      if (
        latestPetItemInfo.current &&
        latestPetItemInfo.current.stamina === latestPetItemInfo.current.currentStamina
      ) {
        clearInterval(interval);
        return;
      }
      sendPetIdleSlowRecovery(serverId);
    }, recoverInterval + 1000);
    return () => {
      clearInterval(interval);
      topUI?.setPetStatus(EntityPetUIStatus.Node);
    };
  }, [entity, serverId, recoverInterval, sendPetIdleSlowRecovery]);
  return null;
}

function PetSpa({
  petEntity,
  serverId,
  recoverInterval,
}: {
  petEntity: Entity;
  serverId: string;
  recoverInterval: number;
}) {
  const [status, setStatus] = useState(SpaStatus.Idle);
  const [poolPos, setPoolPos] = useState<THREE.Vector3 | null>(null);

  useEffect(() => {
    if (status === SpaStatus.Idle) {
      setPoolPos(null);
      petEntity.getComponent<EntityAnimation>(ComponentType.Animation)?.playAnimation(AnimationType.Idle);
      const timer = setTimeout(() => {
        setStatus(SpaStatus.FindPoolPoint);
      }, 1000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [status]);

  return (
    <>
      {status === SpaStatus.FindPoolPoint && (
        <FindClosePool
          findSuccess={(poolPos) => {
            setPoolPos(poolPos);
            setStatus(SpaStatus.MoveToPool);
          }}
        />
      )}
      {status === SpaStatus.MoveToPool && poolPos && (
        <MoveToPool
          entity={petEntity}
          poolPos={poolPos}
          moveToPoolEnd={() => {
            setStatus(SpaStatus.Recovery);
          }}
        />
      )}
      {status === SpaStatus.Recovery && (
        <Recovery serverId={serverId} entity={petEntity} recoverInterval={recoverInterval} />
      )}
    </>
  );
}

export default PetSpa;
