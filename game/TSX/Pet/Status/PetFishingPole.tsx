/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { Entity } from '@/game/TS/Entity/Entity';
import { memo, useEffect, useRef, useState } from 'react';
import { PetFeaturesData } from '@/game/Config/PetFeaturesConfig';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { AnimationType } from '@/game/TSX/Pet/PetEntity';
import usePetWork from '@/hooks/usePetWork';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { useFrame } from '@react-three/fiber';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityEffect } from '@/game/TS/Entity/Components/EntityEffect';
import useWalkPath from '@/game/useWalkPath';

enum FishingStatus {
  MoveToWater,
  WaitingJump,
  JumpToWater,
  CatchFish,
  OutOfWater,
}

const JumpLineList: { start: THREE.Vector3; end: THREE.Vector3; distance: number }[] = [];
const addJumpLine = (start: THREE.Vector3, end: THREE.Vector3) => {
  const distance = start.distanceTo(end);
  JumpLineList.push({ start, end, distance });
};
// addJumpLine()
addJumpLine(new THREE.Vector3(-121.5, -2, 17.5), new THREE.Vector3(-127.3, -2, 19.2));
addJumpLine(new THREE.Vector3(-127.3, -2, 19.2), new THREE.Vector3(-131.8, -2, 21.6));
addJumpLine(new THREE.Vector3(-131.8, -2, 21.6), new THREE.Vector3(-137, -2, 24.5));

const getJumpPosition = () => {
  const totalDistance = JumpLineList.reduce((pre, cur) => pre + cur.distance, 0);
  const randomDistance = Math.random() * totalDistance;
  let currentDistance = 0;
  for (let i = 0; i < JumpLineList.length; i++) {
    const line = JumpLineList[i];
    currentDistance += line.distance;
    if (randomDistance <= currentDistance) {
      const normal = line.start.clone().sub(line.end).normalize();
      return line.end.clone().add(normal.multiplyScalar(currentDistance - randomDistance));
    }
  }
  return new THREE.Vector3(-121.5, -2, 17.5);
};

function MoveToWater({
  entity,
  endCallback,
}: {
  entity: Entity;
  endCallback: (targetPoint: THREE.Vector3) => void;
}) {
  const { path, pathIndex, setTargetPosition } = useWalkPath({ entity });

  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (path.length > 0 && animation) {
      animation.playAnimation(AnimationType.Walk);
      if (pathIndex === path.length) {
        animation.playAnimation(AnimationType.Idle);
        endCallback(path[pathIndex - 1]);
      }
    }
  }, [path, pathIndex, endCallback, entity]);

  useEffect(() => {
    const targetPoint = getJumpPosition();
    setTargetPosition(targetPoint);
  }, []);
  return null;
}

function JumpToWater({
  entity,
  targetPosition,
  endCallback,
}: {
  entity: Entity;
  targetPosition: THREE.Vector3;
  endCallback: () => void;
}) {
  const showEffectRef = useRef(false);
  useEffect(() => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    const targetPoint = new THREE.Vector3(targetPosition.x, -3, targetPosition.z + 5);
    if (!walkPoint || !animation) return;
    walkPoint.setTarget(targetPoint, 5);
    animation.playAnimation(AnimationType.EnterWater);
    showEffectRef.current = false;
    walkPoint.setArriveCallback((isTransform: boolean) => {
      animation.playAnimation(AnimationType.Idle);
      endCallback();
    });
  }, [entity, targetPosition, endCallback]);

  useFrame(() => {
    if (showEffectRef.current) return;
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!walkPoint || !animation) return;

    if (walkPoint.position && walkPoint.quaternion && walkPoint.position.y < -2.2) {
      showEffectRef.current = true;
      const waterEffect = EntityManager.getInstance().createClientEntity([
        ComponentType.Transform,
        ComponentType.Effect,
      ]);
      const transform = waterEffect.getComponent<EntityTransform>(ComponentType.Transform);
      transform?.setPosition(walkPoint.position, walkPoint.quaternion);
      const effect = waterEffect.getComponent<EntityEffect>(ComponentType.Effect);
      effect?.setEffect('./particles/Effect_water_splash.json', 3);
      setTimeout(() => {
        EntityManager.getInstance().removeEntity(waterEffect);
      }, 3000);
    }
  });
  return null;
}

function CatchFish({
  haloEntity,
  features,
  entity,
  endCallback,
}: {
  entity: Entity;
  haloEntity: Entity | null;
  features: PetFeaturesData;
  endCallback: () => void;
}) {
  useEffect(() => {
    const glbMesh = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    const haloMesh = haloEntity?.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (!glbMesh || !walkPoint || !walkPoint.position) return;
    glbMesh.setVisible(false, 'pet_eyes');
    glbMesh.setVisible(false, 'pet_body');
    haloMesh?.setVisible(false, 'light');
    // const effectEntity = EntityManager.getInstance().createClientEntity([
    //   ComponentType.Transform,
    //   ComponentType.Effect,
    // ]);
    // const effect = effectEntity.getComponent<EntityEffect>(ComponentType.Effect);
    // effect?.setEffect('./particles/Effect_water_splash_01.json', 1);
    // const transform = effectEntity.getComponent<EntityTransform>(ComponentType.Transform);
    // transform?.setPosition(
    //   new THREE.Vector3(walkPoint.position.x, -2, walkPoint.position.z),
    //   new THREE.Quaternion()
    // );
    const timer = setTimeout(() => {
      endCallback();
    }, features.interval);
    return () => {
      glbMesh.setVisible(true, 'pet_eyes');
      glbMesh.setVisible(true, 'pet_body');
      haloMesh?.setVisible(true, 'light');
      clearTimeout(timer);
      // EntityManager.getInstance().removeEntity(effectEntity);
    };
  }, [endCallback, entity, haloEntity, features]);
  return null;
}

function OutOfWater({
  serverId,
  entity,
  targetPosition,
  endCallback,
}: {
  targetPosition: THREE.Vector3;
  serverId: string;
  entity: Entity;
  endCallback: () => void;
}) {
  const { handleSocketPetFishing } = usePetWork();
  const showEffectRef = useRef(false);
  useEffect(() => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!walkPoint || !animation) return;
    showEffectRef.current = false;
    walkPoint.setTarget(targetPosition, 5);
    animation.playAnimation(AnimationType.LeaveWater);
    walkPoint.setArriveCallback((isTransform: boolean) => {
      animation.playAnimation(AnimationType.Standby);
      handleSocketPetFishing(serverId);
      endCallback();
    });
  }, []);

  useFrame(() => {
    if (showEffectRef.current) return;
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!walkPoint || !animation) return;

    if (walkPoint.position && walkPoint.quaternion && walkPoint.position.y > -2.2) {
      showEffectRef.current = true;
      showEffectRef.current = true;
      const waterEffect = EntityManager.getInstance().createClientEntity([
        ComponentType.Transform,
        ComponentType.Effect,
      ]);
      const transform = waterEffect.getComponent<EntityTransform>(ComponentType.Transform);
      transform?.setPosition(walkPoint.position, walkPoint.quaternion);
      const effect = waterEffect.getComponent<EntityEffect>(ComponentType.Effect);
      effect?.setEffect('./particles/Effect_water_splash.json', 3);
      setTimeout(() => {
        EntityManager.getInstance().removeEntity(waterEffect);
      }, 3000);
    }
  });
  return null;
}

function PetFishingPole({
  serverId,
  petEntity,
  haloEntity,
  features,
}: {
  serverId: string;
  petEntity: Entity;
  haloEntity: Entity | null;
  features: PetFeaturesData;
}) {
  const [status, setStatus] = useState(FishingStatus.MoveToWater);
  const targetPositionRef = useRef(new THREE.Vector3(0, 0, 0));
  useEffect(() => {
    if (status === FishingStatus.WaitingJump) {
      const timer = setTimeout(() => {
        setStatus(FishingStatus.JumpToWater);
      }, 2000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, [status]);

  return (
    <>
      {status === FishingStatus.MoveToWater && (
        <MoveToWater
          entity={petEntity}
          endCallback={(targetPoint) => {
            targetPositionRef.current.copy(targetPoint);
            setStatus(FishingStatus.WaitingJump);
          }}
        />
      )}
      {status === FishingStatus.JumpToWater && (
        <JumpToWater
          entity={petEntity}
          targetPosition={targetPositionRef.current}
          endCallback={() => {
            setStatus(FishingStatus.CatchFish);
          }}
        />
      )}
      {status === FishingStatus.CatchFish && (
        <CatchFish
          haloEntity={haloEntity}
          features={features}
          entity={petEntity}
          endCallback={() => {
            setStatus(FishingStatus.OutOfWater);
          }}
        />
      )}
      {status === FishingStatus.OutOfWater && (
        <OutOfWater
          targetPosition={targetPositionRef.current}
          serverId={serverId}
          entity={petEntity}
          endCallback={() => {
            setStatus(FishingStatus.MoveToWater);
          }}
        />
      )}
    </>
  );
}

export default memo(PetFishingPole);
