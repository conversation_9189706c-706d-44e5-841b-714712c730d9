/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import { AnimationType } from '@/game/TSX/Pet/PetEntity';
import { Entity } from '@/game/TS/Entity/Entity';
import { useEffect } from 'react';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';

function PetStandby({ petEntity }: { petEntity: Entity }) {
  useEffect(() => {
    if (!petEntity) return;
    const walkPoint = petEntity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);

    if (walkPoint && walkPoint.position && walkPoint.quaternion) {
      const oldQuaternion = walkPoint.quaternion.clone();
      walkPoint.setTarget(walkPoint.position);
      walkPoint.setArriveCallback((isTransform: boolean) => {
        const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
        animation?.playAnimation(AnimationType.Standby);
        walkPoint.quaternion?.copy(oldQuaternion);
      });
    }
  }, [petEntity]);

  return null;
}

export default PetStandby;
