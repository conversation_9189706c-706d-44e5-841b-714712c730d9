import React, { useEffect } from 'react';
import * as THREE from 'three';

import { LoaderUtil, preloadParticleJson } from '@/game/TSX/Util/LoaderUtil';
import { generateUUID } from 'three/src/math/MathUtils';
import ParticleObject from '@/game/TSX/Particles/ParticleObject';

class ParticleItem {
  id: string;
  url: string;
  scale: number;
  position: THREE.Vector3;
  quaternion: THREE.Quaternion;

  constructor(position: THREE.Vector3, quaternion: THREE.Quaternion, url: string, scale: number) {
    this.id = generateUUID();
    this.position = position;
    this.quaternion = quaternion;
    this.url = url;
    this.scale = scale;

    preloadParticleJson();
  }
}

class ParticleSystemClass {
  particleItemMap: Map<string, ParticleItem> = new Map();

  addParticle(
    position: THREE.Vector3,
    quaternion: THREE.Quaternion,
    url: string,
    scale: number,
    time: number,
    replace = false
  ) {
    const item = new ParticleItem(position, quaternion, url, scale);
    LoaderUtil.loadJson(item.url, (json) => {
      if (time > 0) {
        setTimeout(() => {
          this.particleItemMap.delete(item.id);
          this.changeParticleCallback();
        }, time);
      }
      this.particleItemMap.set(item.id, item);
      this.changeParticleCallback();
    });
  }

  registerParticleChange(changeParticle: () => void) {
    this.changeParticleCallback = changeParticle;
  }

  private changeParticleCallback: () => void = () => undefined;
}

const ParticleSystemUtil = new ParticleSystemClass();

export function getParticleSystem() {
  return ParticleSystemUtil;
}

export default function ParticleSystem() {
  const groupRef = React.useRef<THREE.Group>(null);
  const particleSystemUtil = getParticleSystem();
  const [particleList, setParticleList] = React.useState<ParticleItem[]>([]);

  useEffect(() => {
    particleSystemUtil.registerParticleChange(() => {
      setParticleList([...particleSystemUtil.particleItemMap.values()]);
    });
    return () => {
      particleSystemUtil.registerParticleChange(() => undefined);
      setParticleList([]);
    };
  }, [particleSystemUtil]);

  return (
    <group ref={groupRef}>
      {particleList.map((particle) => (
        <group key={particle.id} position={particle.position} quaternion={particle.quaternion}>
          <ParticleObject url={particle.url} scale={particle.scale} />
        </group>
      ))}
    </group>
  );
}
