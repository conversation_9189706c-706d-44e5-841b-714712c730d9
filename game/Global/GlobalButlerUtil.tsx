import * as THREE from 'three';
import { IAssistantAnswer, SCENE_TYPE } from '@/constant/type';
import { useFrame } from '@react-three/fiber';
import { useEffect, useState } from 'react';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
  TransformData,
} from './GlobalSpaceEvent';
import { Raycaster } from 'three/src/Three.Core';
import { VTTSubtitle } from '@/game/TSX/Util/SpeakUtil';
import GlobalSpace, { GAME_OP_TYPE } from './GlobalSpace';
import { AnswerTask } from '@/game/TSX/SceneUI/TopAnswerUI';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';

import BatterySvg from '@/public/image/gameOpIcon/battery.svg';
import { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';

export type ButlerData = {
  butlerPosition: { x: number; y: number; z: number };
  butlerQuaternion: { x: number; y: number; z: number; w: number };
  butlerSceneType: SCENE_TYPE;
  usePet: string;
  vttData: VTTSubtitle[];
  mp3Url: string;
  visitorPositionList: { x: number; y: number; z: number }[];
};

class GlobalButlerUtil {
  data: ButlerData | null = null;
  private scene: THREE.Scene | null = null;
  private isVisitor = false;
  isEnterVisitor = false;
  entityTransform: EntityTransform | null = null;
  entityAnimation: EntityAnimation | null = null;
  entityChatBubble: EntityChatBubble | null = null;

  setTransform(transform: EntityTransform) {
    this.entityTransform = transform;
  }

  setAnimation(animation: EntityAnimation) {
    this.entityAnimation = animation;
    animation.setDefaultAnimation('Action_00');
    animation.setReplaceAnimations(['Action_00', 'Action_05', 'Action_06', 'Action_07']);
    animation.playAnimation('Action_00');
  }

  setChatBubble(chatBubble: EntityChatBubble) {
    this.entityChatBubble = chatBubble;
  }

  updateEntity() {
    if (this.entityTransform && this.entityChatBubble && this.data) {
      this.entityTransform.setPosition(
        new THREE.Vector3(
          this.data.butlerPosition.x,
          this.data.butlerPosition.y,
          this.data.butlerPosition.z
        ),
        new THREE.Quaternion(
          this.data.butlerQuaternion.x,
          this.data.butlerQuaternion.y,
          this.data.butlerQuaternion.z,
          this.data.butlerQuaternion.w
        )
      );
      this.entityChatBubble.speakUtil.loadVTT(this.data.vttData, this.data.mp3Url);

      this.stopSpeakAction();
    } else {
      console.error('butler data is null', this.entityTransform, this.entityChatBubble, this.data);
    }
  }

  setVisitor(isVisitor: boolean) {
    this.isVisitor = isVisitor;
    if (!isVisitor) {
      this.clearButlerData();
    }
  }

  initButlerData(data: ButlerData | null, scene: THREE.Scene) {
    if (this.data) {
      // console.error('butler data has already init')
      return;
    }
    this.data = {
      butlerPosition: { x: 0, y: 0, z: 0 },
      butlerQuaternion: { x: 0, y: 0, z: 0, w: 1 },
      butlerSceneType: SCENE_TYPE.None,
      usePet: '',
      vttData: [],
      mp3Url: '',
      //默认访客在中间位置出生
      visitorPositionList: [],
    };
    this.scene = scene;
    if (data) {
      this.data = data;
      this.updateEntity();
    }
  }

  clearButlerData() {
    this.data = null;
    this.scene = null;
    GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
      characterType: CharacterType.None,
      position: new THREE.Vector3(0, 0, 0),
    });
  }

  resetData(data: ButlerData) {
    if (this.data && this.isVisitor) {
      const oldSceneType = String(data.butlerSceneType);
      if (oldSceneType === 'Island') {
        data.butlerSceneType = SCENE_TYPE.Island;
      }
      if (data.butlerPosition.x > -100) {
        data.butlerPosition.x -= 150;
        for (let i = 0; i < data.visitorPositionList.length; i++) {
          data.visitorPositionList[i].x -= 150;
        }
      }
      this.data.vttData = data.vttData;
      this.data.mp3Url = data.mp3Url;
      this.data.visitorPositionList = data.visitorPositionList;
      this.data.usePet = data.usePet;
      this.data.butlerSceneType = data.butlerSceneType;
      this.data.butlerPosition = data.butlerPosition;
      this.data.butlerQuaternion = data.butlerQuaternion;
      this.updateEntity();
    } else {
      setTimeout(() => {
        this.resetData(data);
      }, 100);
    }
  }

  enterVisitor() {
    if (this.data && this.data.butlerSceneType) {
      GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game);
      const randomPos = this.getRandomVisitorTransform();
      const butlerPosition = this.data.butlerPosition;
      const direction = randomPos.clone().sub(butlerPosition).normalize();
      GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
        position: new THREE.Vector3(randomPos.x, randomPos.y, randomPos.z),
        characterType: CharacterType.Player,
        camDirection: direction,
        sceneType: this.data.butlerSceneType,
      });
      setTimeout(() => {
        this.isEnterVisitor = true;
      }, 1000);
    } else {
      GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
        position: new THREE.Vector3(0, 1, 0),
        characterType: CharacterType.Player,
        sceneType: SCENE_TYPE.Room,
      });
    }
  }

  getButlerData() {
    return this.data;
  }

  updateVisitorTransform(justShowPoint = false) {
    if (this.data && this.scene) {
      const scene = this.scene;
      if (justShowPoint) {
        const sphereList: THREE.Mesh[] = [];
        // 你还可以在场景中高亮显示这些被遮挡的点
        this.data.visitorPositionList.forEach((point) => {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({ color: 0xff0000 }); // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.set(point.x, point.y, point.z);
          sphereList.push(sphere);
          scene.add(sphere);
        });

        setTimeout(() => {
          sphereList.forEach((sphere) => {
            scene.remove(sphere);
          });
        }, 5000);
        return;
      }

      function getSectorPoints(
        center: THREE.Vector3,
        radius: number,
        angle: number,
        quaternion: THREE.Quaternion,
        segments = 32
      ) {
        const points = [];
        const halfAngle = angle / 2;
        const angleStep = angle / segments;

        for (let i = -halfAngle; i <= halfAngle; i += angleStep) {
          const theta = THREE.MathUtils.degToRad(i + 90);
          const x = radius * Math.cos(theta);
          const z = radius * Math.sin(theta);
          const point = new THREE.Vector3(x, 0, z);

          // 应用四元数旋转
          point.applyQuaternion(quaternion);

          // 平移至中心点
          point.add(center);

          points.push(point);
        }

        return points;
      }

      // 设置中心点和半径
      const center = new THREE.Vector3().copy(this.data.butlerPosition); // 中心点坐标
      const quaternion = new THREE.Quaternion().copy(this.data.butlerQuaternion); // 中心点坐标
      center.y += 2;
      const radius = 5; // 访客出生点半径
      const angle = 120; // 扇形角度

      // 生成若干个坐标点
      const points = getSectorPoints(center, radius, angle, quaternion, 12);

      // 创建Raycaster实例
      const raycaster = new Raycaster();

      // 创建一个数组，用于存储被遮挡的点
      const noBlockedPoints: THREE.Vector3[] = [];

      //解决特效object导致的报错
      const list: THREE.Object3D[] = [];
      scene.traverse((value) => {
        if (value.type === 'Mesh') {
          list.push(value);
        }
      });
      // 遍历每个生成的点
      points.forEach((point) => {
        // 从中心点到生成的点发射射线
        const direction = point.clone().sub(center).normalize();
        raycaster.set(center, direction);
        raycaster.far = radius + 1; //多检测1的长度 , 给出生点留出体积

        // 获取射线与场景中物体的相交情况
        const intersects = raycaster.intersectObjects(list);

        // 如果射线与物体相交，说明有遮挡物
        if (intersects.length === 0) {
          noBlockedPoints.push(point);
        }
      });

      if (noBlockedPoints && noBlockedPoints.length === 0) {
        noBlockedPoints.push(center);
      }

      // 输出无遮挡的点
      this.data.visitorPositionList = noBlockedPoints;

      return true;
    }
    return false;
  }

  getRandomVisitorTransform() {
    if (this.data && this.data.visitorPositionList.length > 0) {
      const list = this.data.visitorPositionList;
      if (list && list.length > 0) {
        const index = Math.floor(Math.random() * list.length);
        return new THREE.Vector3(list[index].x, list[index].y, list[index].z);
      }
    }
    return new THREE.Vector3(0, 1, 0);
  }

  setNearButler(callback: () => void) {
    this.nearButler = callback;
  }

  setFarButler(callback: () => void) {
    this.farButler = callback;
  }

  hideButler() {
    if (this.data) {
      this.data.butlerSceneType = SCENE_TYPE.None;
    }
  }

  updateButlerTransform() {
    if (this.data) {
      const data = this.data;
      GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
        GlobalDataKey.SceneType,
        (type) => {
          GlobalSpaceEvent.ListenKeyDataChange<string>(
            GlobalDataKey.UsePetInscriptionId,
            (usePet) => {
              data.butlerSceneType = type;
              const myPlayer = GetMyPlayer();
              data.usePet = usePet;
              data.butlerPosition = {
                x: myPlayer.position.x,
                y: myPlayer.position.y,
                z: myPlayer.position.z,
              };
              data.butlerQuaternion = {
                x: myPlayer.quaternion.x,
                y: myPlayer.quaternion.y,
                z: myPlayer.quaternion.z,
                w: myPlayer.quaternion.w,
              };

              this.updateEntity();

              this.updateVisitorTransform();
            },
            true
          );
        },
        true
      );
      return true;
    }
    return false;
  }

  /**
   *
   * @param vtt_data 播报文案
   * @param mp3_url  播报音频链接
   * @param play 立即播放
   */
  updateBroadcast(vtt_data: VTTSubtitle[], mp3_url: string, play = false) {
    if (this.entityChatBubble) {
      this.entityChatBubble.speakUtil.loadVTT(vtt_data, mp3_url);
      if (play) {
        this.entityChatBubble.speakUtil.wordSpeak(
          '',
          () => {
            this.startSpeakAction();
          },
          () => {
            this.stopSpeakAction();
          }
        );
      }
      return true;
    }
    return false;
  }

  startSpeakAction() {
    if (this.entityAnimation) {
      const list = ['Action_05', 'Action_06', 'Action_07'];
      this.entityAnimation.playAnimation(list[Math.floor(Math.random() * list.length)]);
      return true;
    }
    return false;
  }

  stopSpeakAction() {
    if (this.entityAnimation) {
      this.entityAnimation.playAnimation('Action_00');
      return true;
    }
    return false;
  }

  createAnswer() {
    if (this.entityChatBubble) {
      const task = this.entityChatBubble.createAnswer();
      this.entityChatBubble.speakUtil.stopSpeak();
      return task;
    }
  }

  startAnswerAnim(task: AnswerTask, data: IAssistantAnswer, endCallback: () => void) {
    if (this.entityChatBubble) {
      if (task !== this.entityChatBubble.answerTask) {
        //如果连续请求问题的话，可能存在不一致，旧的任务直接忽略执行
        console.log('ask too fast');
        return;
      }
      this.startSpeakAction();
      task.start(data, () => {
        this.stopSpeakAction();
        endCallback();
      });
    }
  }

  //停止动画
  stopAnswerAnim() {
    if (this.entityChatBubble) {
      this.entityChatBubble.stopAnswer();
    }
  }

  closeAnswer() {
    if (this.entityChatBubble) {
      this.entityChatBubble.clearAnswer();
    }
  }

  nearButler = () => {
    console.log('close butler');
  };

  farButler = () => {
    console.log('far butler');
  };
}

export const ButlerUtil = new GlobalButlerUtil();

export function ButlerTSX() {
  const myPlayer = GetMyPlayer();
  const [sameScene, setSameScene] = useState<boolean>(false);
  const [showButton, setShowButton] = useState<boolean>(false);
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
  const [usePet, setUsePet] = useState<string>('');
  const [avatarData, setAvatarData] = useState<IAvatarMetadata | null>();

  useEffect(() => {
    const sceneTypeKey = GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
      GlobalDataKey.SceneType,
      (type) => {
        setSceneType(type);
      }
    );

    const butlerAvatarDataKey = GlobalSpaceEvent.ListenKeyDataChange<IAvatarMetadata>(
      GlobalDataKey.ButlerAvatarData,
      (avatarData) => {
        setAvatarData(avatarData);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, sceneTypeKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.ButlerAvatarData, butlerAvatarDataKey);
    };
  }, []);

  useEffect(() => {
    if (sameScene) {
      if (usePet.length > 0) {
        const petEntity = EntityManager.getInstance().createClientEntity(
          [
            ComponentType.Transform,
            ComponentType.Animation,
            ComponentType.ChatBubble,
            ComponentType.GlbMesh,
          ],
          true
        );
        petEntity.name = 'ButlerPet';
        const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
        animation && ButlerUtil.setAnimation(animation);
        const chatBubble = petEntity.getComponent<EntityChatBubble>(ComponentType.ChatBubble);
        chatBubble && ButlerUtil.setChatBubble(chatBubble);
        const transform = petEntity.getComponent<EntityTransform>(ComponentType.Transform);
        transform && ButlerUtil.setTransform(transform);
        ButlerUtil.updateEntity();

        return () => {
          EntityManager.getInstance().removeEntity(petEntity);
        };
      } else if (avatarData) {
        const avatarEntity = EntityManager.getInstance().createClientEntity(
          [
            ComponentType.Transform,
            ComponentType.AvatarMesh,
            ComponentType.Animation,
            ComponentType.Face,
            ComponentType.ChatBubble,
          ],
          true
        );
        avatarEntity.name = 'ButlerAvatar';
        const animation = avatarEntity.getComponent<EntityAnimation>(ComponentType.Animation);
        animation && ButlerUtil.setAnimation(animation);
        const chatBubble = avatarEntity.getComponent<EntityChatBubble>(ComponentType.ChatBubble);
        chatBubble && ButlerUtil.setChatBubble(chatBubble);
        const transform = avatarEntity.getComponent<EntityTransform>(ComponentType.Transform);
        transform && ButlerUtil.setTransform(transform);
        const avatarMesh = avatarEntity.getComponent<EntityAvatarMesh>(ComponentType.AvatarMesh);
        avatarMesh && avatarMesh.setAvatar(avatarData);
        const face = avatarEntity.getComponent<EntityFace>(ComponentType.Face);
        face && face.setFaceData('Face', 'avatar');
        ButlerUtil.updateEntity();
        return () => {
          EntityManager.getInstance().removeEntity(avatarEntity);
        };
      }
    }
  }, [usePet, avatarData, sameScene]);

  useEffect(() => {
    const opKeyList: string[] = [];
    if (showButton) {
      if (myPlayer.showEasterEggButton) {
        opKeyList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              myPlayer.callAppApi(AppGameApiKey.finishFishEgg);
              myPlayer.showEasterEggButton = false;
              AudioSystem.playAudio('myPlayer', './sound/reward/reward_axe.mp3', () => {
                return true;
              });
              opKeyList.forEach((key) => {
                GlobalSpace.removeGameOp(key);
              });
            },
            0,
            'Game Clues',
            BatterySvg.src
          )
        );
      }
    }
    return () => {
      opKeyList.forEach((key) => {
        GlobalSpace.removeGameOp(key);
      });
    };
  }, [showButton]);

  useEffect(() => {
    if (sameScene) {
      let enterRange = true;
      const cancelPositionKey = GlobalSpace.whatCharacterPosition((position) => {
        if (ButlerUtil.data && ButlerUtil.isEnterVisitor) {
          const worldPos = ButlerUtil.data.butlerPosition;
          const distance = position.distanceTo(worldPos);
          const nowEnterRange = distance < 3;
          if (nowEnterRange != enterRange) {
            if (nowEnterRange) {
              ButlerUtil.nearButler();
              setShowButton(true);
              ButlerUtil.entityChatBubble?.speakUtil.wordSpeak(
                '',
                () => {
                  ButlerUtil.startSpeakAction();
                },
                () => {
                  ButlerUtil.stopSpeakAction();
                }
              );
            } else {
              setShowButton(false);
              ButlerUtil.farButler();
            }
            enterRange = nowEnterRange;
          }
        }
      });
      return () => {
        GlobalSpace.cancelCharacterPositionCallback(cancelPositionKey);
      };
    }
  }, [sameScene]);

  useFrame(({ clock }) => {
    if (ButlerUtil.data) {
      setSameScene(sceneType === ButlerUtil.data.butlerSceneType);
      setUsePet(ButlerUtil.data.usePet);
    }
  });

  return null;
}
