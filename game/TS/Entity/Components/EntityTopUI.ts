import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateType = 1,
  DeleteType,
}

export enum EntityTopUIType {
  None,
  PetStatus,
}

export enum EntityPetUIStatus {
  Node,
  Spa,
}

export class EntityTopUI extends EntityComponent {
  uiTypeMap: Map<EntityTopUIType, string[]> = new Map();

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateType, game.CommonMessage, (data: game.CommonMessage) => {
      const type = Number(data.messageList[0]) as EntityTopUIType;
      data.messageList.shift();
      this.uiTypeMap.set(type, data.messageList);
      this.selfUpdate();
    });

    this.registerAction(ActionType.DeleteType, game.CommonMessage, (data: game.CommonMessage) => {
      const type = Number(data.messageList[0]) as EntityTopUIType;
      this.uiTypeMap.delete(type);
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.TopUI;
  }

  override syncToServer() {
    this.uiTypeMap.forEach((_, type) => {
      this.sendTypeUpdate(type);
    });
  }

  private sendTypeUpdate(type: EntityTopUIType) {
    const params = this.uiTypeMap.get(type);
    if (params) {
      this.sendAction(
        ActionType.UpdateType,
        game.CommonMessage.create({
          messageList: [String(type), ...params],
        }).toJSON()
      );
    }
  }

  private sendDeleteType(type: EntityTopUIType) {
    this.sendAction(
      ActionType.DeleteType,
      game.CommonMessage.create({
        messageList: [String(type)],
      }).toJSON()
    );
  }

  setPetStatus(status: EntityPetUIStatus) {
    const type = EntityTopUIType.PetStatus;
    const params = [String(status)];
    this.uiTypeMap.set(type, params);
    this.sendTypeUpdate(type);
    this.selfUpdate();
  }

  deleteType(type: EntityTopUIType) {
    this.uiTypeMap.delete(type);
    this.sendDeleteType(type);
    this.selfUpdate();
  }
}
