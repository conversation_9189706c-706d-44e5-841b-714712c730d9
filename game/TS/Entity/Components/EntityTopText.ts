import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateText = 1,
  UpdateIcon = 2,
}

export class EntityTopText extends EntityComponent {
  topText = '';
  textColor = '#FFFFFF';
  topIconName = '';
  showPetOnlyStyle = 0;
  iconConfig = { x: 0, y: 0, size: 0 };

  // 不需要同步
  hideMyself = false;
  withBorder = false;

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateText, game.CommonMessage, (data: game.CommonMessage) => {
      this.topText = data.messageList[0];
      this.textColor = data.messageList[1];
      this.showPetOnlyStyle = Number(data.messageList[2] ?? 0);
      this.selfUpdate();
    });
    this.registerAction(ActionType.UpdateIcon, game.CommonMessage, (data: game.CommonMessage) => {
      this.topIconName = data.messageList[0];
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.TopText;
  }

  override syncToServer() {
    this.sendText();
    this.sendIconName();
  }

  private sendText() {
    this.sendAction(
      ActionType.UpdateText,
      game.CommonMessage.create({
        messageList: [this.topText, this.textColor, String(this.showPetOnlyStyle)],
      }).toJSON()
    );
  }

  private sendIconName() {
    this.sendAction(
      ActionType.UpdateIcon,
      game.CommonMessage.create({
        messageList: [this.topIconName],
      }).toJSON()
    );
  }

  setText(topText: string, textColor = '#FFFFFF', showPetOnlyStyle = 0) {
    this.topText = topText;
    this.textColor = textColor;
    this.showPetOnlyStyle = showPetOnlyStyle;
    this.selfUpdate();
    this.sendText();
  }

  setTopIconName(iconName: string, iconConfig = { x: 0, y: 0, size: 0 }) {
    this.topIconName = iconName;
    this.iconConfig = iconConfig;
    this.selfUpdate();
    this.sendIconName();
  }

  setHideMyself(hide: boolean) {
    this.hideMyself = hide;
    this.selfUpdate();
  }

  setWithBorder(withBorder: boolean) {
    this.withBorder = withBorder;
    this.selfUpdate();
  }
}
