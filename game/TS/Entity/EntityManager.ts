import { Entity } from './Entity';
import { ComponentType, EntityEvent } from './Enum';
import { game } from '@/game/Proto/generated/game_messages';
import { useNetWork } from '@/game/TS/useNetWork';
import { EntityListener } from '@/game/TS/Entity/EntityListener';

export class EntityManager {
  private static instance: EntityManager;

  private entityMap: Map<number, Entity> = new Map<number, Entity>();
  private clientEntityMap: Map<string, Entity> = new Map<string, Entity>();
  private isEnterRoom = false;
  private isInit = false;
  private entityIndex = 0;

  static getInstance() {
    if (!EntityManager.instance) {
      EntityManager.instance = new EntityManager();
    }
    return EntityManager.instance;
  }

  constructor() {
    this.isEnterRoom = false;
    this.isInit = false;
  }

  init() {
    if (this.isInit) {
      return;
    }
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { watchRoomStatus } = useNetWork();
    watchRoomStatus((data) => {
      if (data.isEnterRoom) {
        this.enterMap();
      } else {
        this.leaveMap();
      }
    });
  }

  getEntityListByOwnerAddress(ownerAddress: string) {
    const list: Entity[] = [];
    this.entityMap.forEach((entity) => {
      if (entity.ownerAddress === ownerAddress) {
        list.push(entity);
      }
    });
    return list;
  }

  addEntity(entity: Entity) {
    if (entity.clientId.length > 0) {
      if (this.isEnterRoom) {
        if (!entity.isOnlyClient()) {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const { sendEntityEnter } = useNetWork();
          sendEntityEnter(entity.clientIndex, entity.componentList);
        }
      }
      this.clientEntityMap.set(entity.clientId, entity);
    } else {
      this.entityMap.set(entity.entityId, entity);
    }
    EntityListener.getInstance().notifyListener(EntityEvent.AddEntity, entity);
  }

  removeEntity(entity: Entity) {
    if (entity.clientId.length > 0) {
      if (this.isEnterRoom) {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { sendEntityLeave } = useNetWork();
        sendEntityLeave(entity.clientIndex);
      }
      this.clientEntityMap.delete(entity.clientId);
    } else {
      this.entityMap.delete(entity.entityId);
    }
    entity.destroy();
    EntityListener.getInstance().notifyListener(EntityEvent.RemoveEntity, entity);
  }

  //进入联机地图
  enterMap() {
    this.isEnterRoom = true;
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendEntityEnter } = useNetWork();
    this.clientEntityMap.forEach((entity) => {
      if (!entity.isOnlyClient()) {
        sendEntityEnter(entity.clientIndex, entity.componentList);
        entity.syncComponent();
      }
    });
  }

  //离开联机地图
  leaveMap() {
    this.isEnterRoom = false;
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendEntityLeave } = useNetWork();
    this.clientEntityMap.forEach((entity) => {
      if (!entity.isOnlyClient()) {
        sendEntityLeave(entity.clientIndex);
      }
    });
    this.entityMap.clear();
    EntityListener.getInstance().notifyListener(EntityEvent.RemoveEntity, {});
  }

  getEntity(entityId: number) {
    return this.entityMap.get(entityId);
  }

  getClientEntity(clientId: string) {
    return this.clientEntityMap.get(clientId);
  }

  getEntityList() {
    const list: Entity[] = [];
    this.entityMap.forEach((entity) => {
      list.push(entity);
    });
    this.clientEntityMap.forEach((entity) => {
      list.push(entity);
    });
    // console.log('entityList', list);
    return list;
  }

  createClientEntity(componentList: ComponentType[], onlyClient = false) {
    const entity = new Entity(componentList);
    this.entityIndex++;
    entity.setClientId('client_' + this.entityIndex, onlyClient);
    entity.clientIndex = this.entityIndex;
    this.addEntity(entity);
    return entity;
  }

  serverEntityEnter(enter: game.EntityEnter) {
    if (!this.isEnterRoom) {
      // console.error('not enter room');
      return;
    }
    if (!enter.entityId || enter.entityId === 0) {
      console.error('serverEntityEnter entityId is 0');
      return;
    }
    const entity = new Entity(enter.componentList, enter.ownerAddress);
    entity.setEntityId(enter.entityId);
    this.addEntity(entity);
  }

  serverEntityLeave(leave: game.EntityLeave) {
    if (!this.isEnterRoom) {
      // console.error('not enter room');
      return;
    }
    if (!leave.entityId || leave.entityId === 0) {
      console.error('serverEntityLeave entityId is 0');
      return;
    }
    const entity = this.entityMap.get(leave.entityId);
    if (entity) {
      this.removeEntity(entity);
    } else {
      console.error('entity not found', leave.entityId);
    }
  }

  serverComponentAction(action: game.EntityComponentAction) {
    if (!this.isEnterRoom) {
      // console.error('not enter room');
      return;
    }
    if (!action.entityId || action.entityId === 0) {
      console.error('serverComponentAction entityId is 0');
      return;
    }
    const entity = this.entityMap.get(action.entityId);
    if (entity) {
      entity.receiveComponentAction(action.componentType, action.actionType, action.actionData);
    } else {
      console.error('entity not found', action.entityId);
    }
  }
}
