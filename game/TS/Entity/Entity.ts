import * as THREE from 'three';
import { useNetWork } from '@/game/TS/useNetWork';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityComponent } from '@/game/TS/Entity/Components/EntityComponent';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import { EntityEffect } from '@/game/TS/Entity/Components/EntityEffect';
import { EntityPizzaMesh } from '@/game/TS/Entity/Components/EntityPizzaMesh';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { EntityCollider } from '@/game/TS/Entity/Components/EntityCollider';
import { EntityAnimationAll } from '@/game/TS/Entity/Components/EntityAnimationAll';
import { EntityTopUI } from '@/game/TS/Entity/Components/EntityTopUI';

const ComponentClassMap = new Map<ComponentType, any>([
  [ComponentType.Animation, EntityAnimation],
  [ComponentType.Effect, EntityEffect],
  [ComponentType.TopText, EntityTopText],
  [ComponentType.Face, EntityFace],
  [ComponentType.ChatBubble, EntityChatBubble],
  [ComponentType.Collider, EntityCollider],
  [ComponentType.AnimationAll, EntityAnimationAll],
  [ComponentType.TopUI, EntityTopUI],
  [ComponentType.GlbMesh, EntityGlbMesh],
  [ComponentType.AvatarMesh, EntityAvatarMesh],
  [ComponentType.PizzaMesh, EntityPizzaMesh],
  [ComponentType.Transform, EntityTransform],
  [ComponentType.FollowBone, EntityFollowBone],
  [ComponentType.WalkPoint, EntityWalkPoint],
]);

export enum EntityListenerKey {
  VISIBLE_CHANGE = 'visibleChange',
}

export class Entity {
  name = '';
  entityId: number;
  clientId: string;
  clientIndex: number;
  ownerAddress: string;
  componentList: ComponentType[];
  visible: boolean;
  private rootObject: THREE.Object3D | null = null;
  private changeCallbacks: (() => void)[] = [];
  private onlyClient = false;
  private eventListener: Map<EntityListenerKey, ((...args: any[]) => void)[]> = new Map();

  private componentMap: Map<ComponentType, EntityComponent> = new Map<
    ComponentType,
    EntityComponent
  >();

  constructor(componentList: ComponentType[], ownerAddress = '') {
    this.entityId = 0;
    this.clientIndex = 0;
    this.clientId = '';
    this.ownerAddress = ownerAddress;
    this.componentList = componentList;
    this.initComponent(componentList);
    this.visible = true;
  }

  setEntityId(entityId: number) {
    this.entityId = entityId;
  }

  setClientId(clientId: string, onlyClient = false) {
    this.clientId = clientId;
    this.onlyClient = onlyClient;
  }

  isOnlyClient() {
    return this.onlyClient;
  }

  isClientEntity() {
    return this.clientId.length > 0;
  }

  initComponent(componentType: ComponentType[]) {
    componentType.forEach((type) => {
      const componentClass = ComponentClassMap.get(type);
      if (!componentClass) {
        console.error('componentClass not found', type);
        return;
      }
      this.componentMap.set(type, new componentClass(this));
    });
  }

  getComponent<T extends EntityComponent>(type: ComponentType): T | undefined {
    return this.componentMap.get(type) as T;
  }

  syncComponent() {
    this.componentMap.forEach((component) => {
      component.syncToServer();
    });
  }

  receiveComponentAction(componentType: ComponentType, action: number, uint8Array: Uint8Array) {
    const component = this.componentMap.get(componentType);
    if (!component) {
      console.error('component not found', componentType);
      return;
    }
    component.handleAction(action, uint8Array);
  }

  sendComponentAction(componentType: ComponentType, action: number, uint8Array: Uint8Array) {
    if (this.clientId.length === 0) {
      console.error('entityId is 0');
      return;
    }
    if (this.isOnlyClient()) {
      return;
    }
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendEntityComponentAction } = useNetWork();
    sendEntityComponentAction(this.clientIndex, componentType, action, uint8Array);
  }

  setEntityRoot(entityRoot: THREE.Object3D | null) {
    this.rootObject = entityRoot;
    this.notifyChange();
  }

  childrenChange() {
    this.notifyChange();
  }

  notifyChange() {
    this.changeCallbacks.forEach((callback) => callback());
  }

  registerRootChange(callback: () => void) {
    this.changeCallbacks.push(callback);
    if (this.rootObject) {
      callback();
    }
  }

  unregisterRootChange(callback: () => void) {
    this.changeCallbacks = this.changeCallbacks.filter((cb) => cb !== callback);
  }

  findBone(targetBoneName: string) {
    if (!this.rootObject) {
      return;
    }
    const bone = this.rootObject.getObjectByName(targetBoneName);
    if (!bone) {
      return;
    }
    return bone;
  }

  getPosition() {
    if (!this.rootObject) {
      return new THREE.Vector3(0, 0, 0);
    }
    return this.rootObject.position;
  }

  destroy() {
    this.entityId = 0;
    this.clientId = '';
  }

  on(event: EntityListenerKey, callback: (...args: any[]) => void) {
    if (!this.eventListener.has(event)) {
      this.eventListener.set(event, []);
    }
    this.eventListener.get(event)!.push(callback);
  }

  off(event: EntityListenerKey, callback: (...args: any[]) => void) {
    const listeners = this.eventListener.get(event);
    if (listeners) {
      this.eventListener.set(
        event,
        listeners.filter((cb) => cb !== callback)
      );
    }
  }

  emit(event: EntityListenerKey, ...args: any[]) {
    const listeners = this.eventListener.get(event);
    if (listeners) {
      listeners.forEach((callback) => callback(...args));
    }
  }
}
