// import { useRapier } from "@react-three/rapier";
import { useControls } from 'leva';
import { SKETCH } from '@/game_lib/const';

export const useNavMeshDebug = function ({
  boundsDebug = false,
  navMeshDebug = false,
  cellSize = 0.20,
  cellHeight = 0.05,
  walkableSlopeAngle = 35,
  walkableRadius = 0.5,
  walkableClimb = 0.5,
  walkableHeight = 1.8,
}: UseNavMeshProps = {}) {
  const debugPathMesh = localStorage.getItem('debugPathMesh') === 'true';
  if (debugPathMesh) {
    const debugData = useControls(`${SKETCH}-navigation`, {
      boundsDebug: false,
      navMeshDebug: false,
      cellSize: {
        label: 'Cell Size',
        value: cellSize,
        min: 0.05,
        max: 0.5,
        step: 0.01,
      },
      cellHeight: {
        label: 'Cell Height',
        value: cellHeight,
        min: 0.01,
        max: 0.5,
        step: 0.01,
      },
      walkableSlopeAngle: {
        label: 'Walk Slope Angle',
        value: walkableSlopeAngle,
        min: 0,
        max: 90,
        step: 1,
      },
      walkableRadius: {
        label: 'Walk Radius',
        value: walkableRadius,
        min: 0.1,
        max: 1,
        step: 0.1,
      },
      walkableClimb: {
        label: 'Walk Climb',
        value: walkableClimb,
        min: 0.1,
        max: 2,
        step: 0.1,
      },
      walkableHeight: {
        label: 'Walk Height',
        value: walkableHeight,
        min: 0.1,
        max: 3,
        step: 0.1,
      },
    });
    boundsDebug = debugData.boundsDebug;
    navMeshDebug = debugData.navMeshDebug;
    cellSize = debugData.cellSize;
    cellHeight = debugData.cellHeight;
    walkableSlopeAngle = debugData.walkableSlopeAngle;
    walkableClimb = debugData.walkableClimb;
    walkableRadius = debugData.walkableRadius;
    walkableHeight = debugData.walkableHeight;
  }

  return {
    boundsDebug,
    navMeshDebug,
    cellSize,
    cellHeight,
    walkableSlopeAngle,
    walkableClimb,
    walkableRadius,
    walkableHeight,
  };
};

export type UseNavMeshProps = {
  boundsDebug?: boolean;
  navMeshDebug?: boolean;
  cellSize?: number;
  cellHeight?: number;
  walkableSlopeAngle?: number;
  walkableClimb?: number;
  walkableRadius?: number;
  walkableHeight?: number;
};
